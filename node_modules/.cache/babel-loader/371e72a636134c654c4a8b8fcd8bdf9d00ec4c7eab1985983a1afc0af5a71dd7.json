{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Clock } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowDemo = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(-1); // -1表示未开始\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [executionResults, setExecutionResults] = useState(null); // 执行结果详情\n\n  // 工作流步骤定义 - 增强版本，包含失败详情\n  const workflowSteps = [{\n    id: 'login',\n    name: '登录验证',\n    runningDescription: '正在验证Temu账号登录状态...',\n    completedDescription: '已成功登录【Temu账号】名下的店铺1',\n    failedDescription: '登录验证失败，请检查账号状态',\n    duration: 2000,\n    status: 'waiting'\n  }, {\n    id: 'product_filter',\n    name: '商品筛选',\n    runningDescription: '正在筛选符合条件的商品...',\n    completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n    failedDescription: '商品筛选失败，可能是网络连接问题',\n    duration: 3000,\n    status: 'waiting'\n  }, {\n    id: 'product_processing',\n    name: '商品加权处理',\n    runningDescription: '正在为商品设置流量加权配置...',\n    completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',\n    failedDescription: '商品加权处理失败，部分商品无法配置',\n    duration: 4000,\n    status: 'waiting'\n  }, {\n    id: 'result_summary',\n    name: '结果汇总',\n    runningDescription: '正在生成执行结果报告...',\n    completedDescription: '任务执行完成，正在生成详细报告',\n    failedDescription: '结果汇总失败，无法生成完整报告',\n    duration: 1500,\n    status: 'waiting'\n  }];\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = step => {\n    switch (step.status) {\n      case 'running':\n        return step.runningDescription;\n      case 'completed':\n        return step.completedDescription;\n      case 'failed':\n        return step.failedDescription;\n      case 'waiting':\n        return '等待执行...';\n      default:\n        return '等待执行...';\n    }\n  };\n\n  // 生成详细的执行结果报告\n  const generateExecutionReport = () => {\n    const successfulProducts = ['【智能手机壳-透明款】', '【蓝牙耳机-运动版】', '【充电宝-快充版】', '【数据线-Type-C】', '【手机支架-桌面版】', '【无线充电器】'];\n    const failedProducts = [{\n      name: '【高端手机壳-奢华版】',\n      reason: 'AI判断其价格为6.8美元，高于5美元的设定而被跳过'\n    }, {\n      name: '【专业摄影灯】',\n      reason: '页面长时间无法打开而执行失败'\n    }];\n    const report = {\n      totalProcessed: 200,\n      successful: 186,\n      failed: 14,\n      totalCost: 892.40,\n      successfulProducts: successfulProducts.slice(0, 6),\n      failedProducts: failedProducts,\n      executionTime: '8分32秒'\n    };\n    return report;\n  };\n\n  // 模拟工作流执行 - 重新设计的执行逻辑\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setExecutionResults(null);\n\n    // 从失败步骤开始，或从第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是全新开始，重置所有步骤状态\n    if (failedStep === null) {\n      setSteps(prev => prev.map(step => ({\n        ...step,\n        status: 'waiting'\n      })));\n    } else {\n      // 如果是重试，只重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => index === failedStep ? {\n        ...step,\n        status: 'waiting'\n      } : step));\n      setFailedStep(null);\n    }\n\n    // 执行工作流步骤\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'running'\n      } : step));\n\n      // 模拟步骤执行时间\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 模拟随机失败（20%概率，主要在第2和第3步）\n      const shouldFail = Math.random() < 0.2 && (i === 1 || i === 2);\n      if (shouldFail) {\n        // 步骤失败\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'failed'\n        } : step));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 步骤成功完成\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'completed'\n      } : step));\n\n      // 短暂延迟，让用户看到状态变化\n      await new Promise(resolve => setTimeout(resolve, 300));\n    }\n\n    // 所有步骤完成，生成执行结果\n    const report = generateExecutionReport();\n    setExecutionResults(report);\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n    setCurrentStep(-1);\n  };\n\n  // 重试失败的步骤 - 精准重试机制\n  const retryFromFailed = () => {\n    if (failedStep !== null && !isRunning) {\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(-1);\n    setFailedStep(null);\n    setExecutionResults(null);\n    setSteps(workflowSteps.map(step => ({\n      ...step,\n      status: 'waiting'\n    })));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-500 text-white p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold\",\n              children: \"Temu\\u81EA\\u52A8\\u5316\\u52A9\\u624B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-sm\",\n              children: \"\\u6D41\\u91CF\\u52A0\\u901F\\u81EA\\u52A8\\u5316\\u6267\\u884C\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 rounded-lg p-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-800 mb-2\",\n              children: \"\\u597D\\u7684\\uFF0C\\u5F00\\u59CB\\u6267\\u884C\\u8BA1\\u5212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 bg-white rounded-lg p-3 border\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"\\u6267\\u884C\\u914D\\u7F6E\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), \"\\u9AD8\\u7EA7\\u6D41\\u91CF\\u52A0\\u6743\\u6863\\u4F4D\\uFF0C\\u4EF7\\u683C\\u8303\\u56F44-6\\u7F8E\\u5143\\uFF0C\\u65F6\\u654830\\u5929\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: steps.slice(0, visibleSteps).map((step, index) => {\n            const nextStep = steps[index + 1];\n            const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n            const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [shouldShowConnector && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`,\n                style: {\n                  height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4 relative z-10 pb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${step.status === 'completed' ? 'bg-green-500 text-white' : step.status === 'failed' ? 'bg-red-500 text-white' : step.status === 'running' ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'}`,\n                    children: [step.status === 'completed' && /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 57\n                    }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 54\n                    }, this), step.status === 'running' && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 55\n                    }, this), step.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-3 h-3 rounded-full bg-gray-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 55\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0 pt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: step.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 23\n                    }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: retryFromFailed,\n                      disabled: isRunning,\n                      className: \"mt-2 text-xs bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1.5 rounded-full font-medium transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                        className: \"w-3 h-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u91CD\\u8BD5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 leading-relaxed\",\n                    children: getStepDescription(step)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 21\n                  }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2 bg-red-50 border border-red-200 rounded-lg p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-red-700 text-sm\",\n                      children: \"\\u6267\\u884C\\u5931\\u8D25\\uFF0C\\u53EF\\u80FD\\u662F\\u7F51\\u7EDC\\u8FDE\\u63A5\\u95EE\\u9898\\u6216\\u7CFB\\u7EDF\\u7E41\\u5FD9\\uFF0C\\u8BF7\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\\u3002\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [workflowStatus === 'idle' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: executeWorkflow,\n                disabled: isRunning,\n                className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Play, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5F00\\u59CB\\u6267\\u884C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 19\n              }, this), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: true,\n                className: \"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u4E2D...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: resetWorkflow,\n                className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this), workflowStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: retryFromFailed,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u91CD\\u8BD5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\",\n                  children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Loader2, {\n                    className: \"w-4 h-4 text-white animate-spin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-blue-700 font-medium\",\n                  children: [\"\\u6B63\\u5728\\u6267\\u884C\\u7B2C \", currentStep + 1, \" \\u6B65\\uFF0C\\u5171 \", steps.length, \" \\u6B65\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetWorkflow,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\",\n              title: \"\\u91CD\\u65B0\\u5F00\\u59CB\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-green-700 font-semibold text-base mb-1\",\n                  children: \"\\uD83C\\uDF89 \\u81EA\\u52A8\\u5316\\u6267\\u884C\\u5B8C\\u6210\\uFF01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-green-600 text-sm\",\n                  children: \"\\u6240\\u6709\\u5546\\u54C1\\u5DF2\\u6210\\u529F\\u914D\\u7F6E\\u6D41\\u91CF\\u52A0\\u901F\\uFF0C\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), workflowStatus === 'failed' && failedStep !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-700 font-semibold text-base mb-1\",\n                  children: \"\\u6267\\u884C\\u4E2D\\u65AD\\uFF0C\\u9700\\u8981\\u5904\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-600 text-sm\",\n                  children: [\"\\u7B2C \", failedStep + 1, \" \\u6B65\\u51FA\\u73B0\\u95EE\\u9898\\uFF0C\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-white/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-semibold text-gray-900 mb-3 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), \"Demo\\u63A7\\u5236\\u9762\\u677F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 mb-4 leading-relaxed\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium\",\n          children: \"\\u6F14\\u793A\\u7279\\u6027\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-flex items-center ml-2 space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 49\n            }, this), \"\\u52A8\\u6001\\u63CF\\u8FF0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 49\n            }, this), \"\\u65F6\\u95F4\\u7EBF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 49\n            }, this), \"\\u5931\\u8D25\\u91CD\\u8BD5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 49\n            }, this), \"\\u5B8C\\u7F8E\\u8FDE\\u63A5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: executeWorkflow,\n          disabled: isRunning,\n          className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u6A21\\u62DF\\u6267\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetWorkflow,\n          className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u91CD\\u7F6E\\u72B6\\u6001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            // 快速演示失败场景\n            setVisibleSteps(3);\n            setSteps(prev => prev.map((step, index) => {\n              if (index === 0) return {\n                ...step,\n                status: 'completed'\n              };\n              if (index === 1) return {\n                ...step,\n                status: 'completed'\n              };\n              if (index === 2) return {\n                ...step,\n                status: 'failed'\n              };\n              return step;\n            }));\n            setWorkflowStatus('failed');\n            setFailedStep(2);\n          },\n          className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u6F14\\u793A\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(TemuWorkflowDemo, \"fMJvJHTAjkN4KVVgc8nhos3pTik=\");\n_c = TemuWorkflowDemo;\nexport default TemuWorkflowDemo;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowDemo\");", "map": {"version": 3, "names": ["React", "useState", "Play", "RefreshCw", "CheckCircle", "XCircle", "AlertCircle", "Loader2", "Clock", "jsxDEV", "_jsxDEV", "TemuWorkflowDemo", "_s", "currentStep", "setCurrentStep", "isRunning", "setIsRunning", "workflowStatus", "setWorkflowStatus", "failedStep", "setFailedStep", "executionResults", "setExecutionResults", "workflowSteps", "id", "name", "runningDescription", "completedDescription", "failedDescription", "duration", "status", "steps", "setSteps", "getStepDescription", "step", "generateExecutionReport", "successfulProducts", "failedProducts", "reason", "report", "totalProcessed", "successful", "failed", "totalCost", "slice", "executionTime", "executeWorkflow", "startStep", "prev", "map", "index", "i", "length", "Promise", "resolve", "setTimeout", "shouldFail", "Math", "random", "retryFromFailed", "resetWorkflow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "visibleSteps", "nextStep", "shouldShowConnector", "connectorColor", "style", "height", "onClick", "disabled", "title", "setVisibleSteps", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Clock } from 'lucide-react';\n\nconst TemuWorkflowDemo = () => {\n  const [currentStep, setCurrentStep] = useState(-1); // -1表示未开始\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [executionResults, setExecutionResults] = useState(null); // 执行结果详情\n\n  // 工作流步骤定义 - 增强版本，包含失败详情\n  const workflowSteps = [\n    {\n      id: 'login',\n      name: '登录验证',\n      runningDescription: '正在验证Temu账号登录状态...',\n      completedDescription: '已成功登录【Temu账号】名下的店铺1',\n      failedDescription: '登录验证失败，请检查账号状态',\n      duration: 2000,\n      status: 'waiting'\n    },\n    {\n      id: 'product_filter',\n      name: '商品筛选',\n      runningDescription: '正在筛选符合条件的商品...',\n      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n      failedDescription: '商品筛选失败，可能是网络连接问题',\n      duration: 3000,\n      status: 'waiting'\n    },\n    {\n      id: 'product_processing',\n      name: '商品加权处理',\n      runningDescription: '正在为商品设置流量加权配置...',\n      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',\n      failedDescription: '商品加权处理失败，部分商品无法配置',\n      duration: 4000,\n      status: 'waiting'\n    },\n    {\n      id: 'result_summary',\n      name: '结果汇总',\n      runningDescription: '正在生成执行结果报告...',\n      completedDescription: '任务执行完成，正在生成详细报告',\n      failedDescription: '结果汇总失败，无法生成完整报告',\n      duration: 1500,\n      status: 'waiting'\n    }\n  ];\n\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = (step) => {\n    switch (step.status) {\n      case 'running':\n        return step.runningDescription;\n      case 'completed':\n        return step.completedDescription;\n      case 'failed':\n        return step.failedDescription;\n      case 'waiting':\n        return '等待执行...';\n      default:\n        return '等待执行...';\n    }\n  };\n\n  // 生成详细的执行结果报告\n  const generateExecutionReport = () => {\n    const successfulProducts = [\n      '【智能手机壳-透明款】', '【蓝牙耳机-运动版】', '【充电宝-快充版】',\n      '【数据线-Type-C】', '【手机支架-桌面版】', '【无线充电器】'\n    ];\n\n    const failedProducts = [\n      { name: '【高端手机壳-奢华版】', reason: 'AI判断其价格为6.8美元，高于5美元的设定而被跳过' },\n      { name: '【专业摄影灯】', reason: '页面长时间无法打开而执行失败' }\n    ];\n\n    const report = {\n      totalProcessed: 200,\n      successful: 186,\n      failed: 14,\n      totalCost: 892.40,\n      successfulProducts: successfulProducts.slice(0, 6),\n      failedProducts: failedProducts,\n      executionTime: '8分32秒'\n    };\n\n    return report;\n  };\n\n  // 模拟工作流执行 - 重新设计的执行逻辑\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setExecutionResults(null);\n\n    // 从失败步骤开始，或从第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是全新开始，重置所有步骤状态\n    if (failedStep === null) {\n      setSteps(prev => prev.map(step => ({ ...step, status: 'waiting' })));\n    } else {\n      // 如果是重试，只重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) =>\n        index === failedStep ? { ...step, status: 'waiting' } : step\n      ));\n      setFailedStep(null);\n    }\n\n    // 执行工作流步骤\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'running' } : step\n      ));\n\n      // 模拟步骤执行时间\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 模拟随机失败（20%概率，主要在第2和第3步）\n      const shouldFail = Math.random() < 0.2 && (i === 1 || i === 2);\n\n      if (shouldFail) {\n        // 步骤失败\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'failed' } : step\n        ));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 步骤成功完成\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'completed' } : step\n      ));\n\n      // 短暂延迟，让用户看到状态变化\n      await new Promise(resolve => setTimeout(resolve, 300));\n    }\n\n    // 所有步骤完成，生成执行结果\n    const report = generateExecutionReport();\n    setExecutionResults(report);\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n    setCurrentStep(-1);\n  };\n\n  // 重试失败的步骤 - 精准重试机制\n  const retryFromFailed = () => {\n    if (failedStep !== null && !isRunning) {\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(-1);\n    setFailedStep(null);\n    setExecutionResults(null);\n    setSteps(workflowSteps.map(step => ({ ...step, status: 'waiting' })));\n  };\n\n  return (\n    <div className=\"w-full\">\n      {/* AI对话框容器 */}\n      <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\">\n        {/* 对话框头部 */}\n        <div className=\"bg-blue-500 text-white p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-sm font-bold\">AI</span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold\">Temu自动化助手</h3>\n              <p className=\"text-blue-100 text-sm\">流量加速自动化执行中...</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 对话内容 */}\n        <div className=\"p-6\">\n          {/* AI消息 */}\n          <div className=\"mb-6\">\n            <div className=\"bg-gray-100 rounded-lg p-4 mb-4\">\n              <p className=\"text-gray-800 mb-2\">好的，开始执行计划</p>\n              <div className=\"text-sm text-gray-600 bg-white rounded-lg p-3 border\">\n                <span className=\"font-medium\">执行配置：</span>高级流量加权档位，价格范围4-6美元，时效30天\n              </div>\n            </div>\n          </div>\n\n          {/* 优化后的时间线步骤列表 - 完美连接线 */}\n          <div className=\"relative\">\n            {steps.slice(0, visibleSteps).map((step, index) => {\n              const nextStep = steps[index + 1];\n              const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n              const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n\n              return (\n                <div key={step.id} className=\"relative\">\n                  {/* 连接线 - 只在步骤完成后显示绿色，否则显示灰色 */}\n                  {shouldShowConnector && (\n                    <div\n                      className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`}\n                      style={{\n                        height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                      }}\n                    ></div>\n                  )}\n\n                  {/* 步骤内容容器 */}\n                  <div className=\"flex items-start space-x-4 relative z-10 pb-6\">\n                    {/* 状态图标容器 - 优化层级确保完美覆盖 */}\n                    <div className=\"flex-shrink-0 relative\">\n                      {/* 图标背景圆圈 - 确保完全覆盖连接线 */}\n                      <div className=\"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"></div>\n\n                      {/* 状态图标 - 最高层级 */}\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${\n                        step.status === 'completed' ? 'bg-green-500 text-white' :\n                        step.status === 'failed' ? 'bg-red-500 text-white' :\n                        step.status === 'running' ? 'bg-blue-500 text-white' :\n                        'bg-gray-300 text-gray-600'\n                      }`}>\n                        {step.status === 'completed' && <CheckCircle className=\"w-5 h-5\" />}\n                        {step.status === 'failed' && <XCircle className=\"w-5 h-5\" />}\n                        {step.status === 'running' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                        {step.status === 'pending' && <div className=\"w-3 h-3 rounded-full bg-gray-600\"></div>}\n                      </div>\n                    </div>\n\n                  {/* 步骤内容 */}\n                  <div className=\"flex-1 min-w-0 pt-1\">\n                    <div className=\"mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">{step.name}</h4>\n                      {/* 失败状态的重试按钮 */}\n                      {step.status === 'failed' && (\n                        <button\n                          onClick={retryFromFailed}\n                          disabled={isRunning}\n                          className=\"mt-2 text-xs bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1.5 rounded-full font-medium transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-1\"\n                        >\n                          <RefreshCw className=\"w-3 h-3\" />\n                          <span>重试</span>\n                        </button>\n                      )}\n                    </div>\n\n                    <p className=\"text-sm text-gray-600 leading-relaxed\">\n                      {getStepDescription(step)}\n                    </p>\n\n                    {/* 失败时的错误信息 */}\n                    {step.status === 'failed' && (\n                      <div className=\"mt-2 bg-red-50 border border-red-200 rounded-lg p-3\">\n                        <p className=\"text-red-700 text-sm\">\n                          执行失败，可能是网络连接问题或系统繁忙，请点击重试继续执行。\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            );\n            })}\n          </div>\n\n          {/* 操作按钮区域 */}\n          <div className=\"mt-8 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                {workflowStatus === 'idle' && (\n                  <button\n                    onClick={executeWorkflow}\n                    disabled={isRunning}\n                    className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                  >\n                    <Play className=\"w-4 h-4\" />\n                    <span>开始执行</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'running' && (\n                  <button\n                    disabled\n                    className=\"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span>执行中...</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'completed' && (\n                  <button\n                    onClick={resetWorkflow}\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                  >\n                    <CheckCircle className=\"w-4 h-4\" />\n                    <span>执行完成</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'failed' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={retryFromFailed}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <RefreshCw className=\"w-4 h-4\" />\n                      <span>重试</span>\n                    </button>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\"\n                    >\n                      重新开始\n                    </button>\n                  </div>\n                )}\n\n                {/* 整体进度指示 */}\n                {workflowStatus === 'running' && (\n                  <div className=\"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\">\n                      <Loader2 className=\"w-4 h-4 text-white animate-spin\" />\n                    </div>\n                    <span className=\"text-sm text-blue-700 font-medium\">\n                      正在执行第 {currentStep + 1} 步，共 {steps.length} 步\n                    </span>\n                  </div>\n                )}\n              </div>\n\n              {/* 刷新按钮 */}\n              <button\n                onClick={resetWorkflow}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\"\n                title=\"重新开始\"\n              >\n                <RefreshCw className=\"w-4 h-4\" />\n              </button>\n            </div>\n\n            {/* 状态指示器 */}\n            {workflowStatus === 'completed' && (\n              <div className=\"mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <CheckCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-green-700 font-semibold text-base mb-1\">\n                      🎉 自动化执行完成！\n                    </div>\n                    <div className=\"text-green-600 text-sm\">\n                      所有商品已成功配置流量加速，系统运行正常\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {workflowStatus === 'failed' && failedStep !== null && (\n              <div className=\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-red-700 font-semibold text-base mb-1\">\n                      执行中断，需要处理\n                    </div>\n                    <div className=\"text-red-600 text-sm\">\n                      第 {failedStep + 1} 步出现问题，可点击重试继续执行\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Demo控制面板 */}\n      <div className=\"mt-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-white/20\">\n        <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center\">\n          <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></div>\n          Demo控制面板\n        </h4>\n        <p className=\"text-sm text-gray-600 mb-4 leading-relaxed\">\n          <span className=\"font-medium\">演示特性：</span>\n          <span className=\"inline-flex items-center ml-2 space-x-3\">\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />动态描述</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />时间线</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />失败重试</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />完美连接</span>\n          </span>\n        </p>\n        <div className=\"flex flex-wrap gap-3\">\n          <button\n            onClick={executeWorkflow}\n            disabled={isRunning}\n            className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>🚀</span>\n            <span>模拟执行</span>\n          </button>\n          <button\n            onClick={resetWorkflow}\n            className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>🔄</span>\n            <span>重置状态</span>\n          </button>\n          <button\n            onClick={() => {\n              // 快速演示失败场景\n              setVisibleSteps(3);\n              setSteps(prev => prev.map((step, index) => {\n                if (index === 0) return { ...step, status: 'completed' };\n                if (index === 1) return { ...step, status: 'completed' };\n                if (index === 2) return { ...step, status: 'failed' };\n                return step;\n              }));\n              setWorkflowStatus('failed');\n              setFailedStep(2);\n            }}\n            className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>⚠️</span>\n            <span>演示失败</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TemuWorkflowDemo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElG,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAMsB,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,mBAAmB;IACvCC,oBAAoB,EAAE,qBAAqB;IAC3CC,iBAAiB,EAAE,gBAAgB;IACnCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,2CAA2C;IACjEC,iBAAiB,EAAE,kBAAkB;IACrCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,oBAAoB;IACxBC,IAAI,EAAE,QAAQ;IACdC,kBAAkB,EAAE,kBAAkB;IACtCC,oBAAoB,EAAE,2BAA2B;IACjDC,iBAAiB,EAAE,mBAAmB;IACtCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,eAAe;IACnCC,oBAAoB,EAAE,iBAAiB;IACvCC,iBAAiB,EAAE,iBAAiB;IACpCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAACsB,aAAa,CAAC;;EAEjD;EACA,MAAMU,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI,CAACJ,MAAM;MACjB,KAAK,SAAS;QACZ,OAAOI,IAAI,CAACR,kBAAkB;MAChC,KAAK,WAAW;QACd,OAAOQ,IAAI,CAACP,oBAAoB;MAClC,KAAK,QAAQ;QACX,OAAOO,IAAI,CAACN,iBAAiB;MAC/B,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;;EAED;EACA,MAAMO,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,kBAAkB,GAAG,CACzB,aAAa,EAAE,YAAY,EAAE,WAAW,EACxC,cAAc,EAAE,YAAY,EAAE,SAAS,CACxC;IAED,MAAMC,cAAc,GAAG,CACrB;MAAEZ,IAAI,EAAE,aAAa;MAAEa,MAAM,EAAE;IAA6B,CAAC,EAC7D;MAAEb,IAAI,EAAE,SAAS;MAAEa,MAAM,EAAE;IAAiB,CAAC,CAC9C;IAED,MAAMC,MAAM,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,UAAU,EAAE,GAAG;MACfC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,MAAM;MACjBP,kBAAkB,EAAEA,kBAAkB,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAClDP,cAAc,EAAEA,cAAc;MAC9BQ,aAAa,EAAE;IACjB,CAAC;IAED,OAAON,MAAM;EACf,CAAC;;EAED;EACA,MAAMO,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC9B,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,SAAS,CAAC;IAC5BI,mBAAmB,CAAC,IAAI,CAAC;;IAEzB;IACA,MAAMyB,SAAS,GAAG5B,UAAU,KAAK,IAAI,GAAGA,UAAU,GAAG,CAAC;IACtDL,cAAc,CAACiC,SAAS,CAAC;;IAEzB;IACA,IAAI5B,UAAU,KAAK,IAAI,EAAE;MACvBa,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACf,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC,MAAM;MACL;MACAE,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KACpCA,KAAK,KAAK/B,UAAU,GAAG;QAAE,GAAGe,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,GAAGI,IAC1D,CAAC,CAAC;MACFd,aAAa,CAAC,IAAI,CAAC;IACrB;;IAEA;IACA,KAAK,IAAI+B,CAAC,GAAGJ,SAAS,EAAEI,CAAC,GAAGpB,KAAK,CAACqB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7CrC,cAAc,CAACqC,CAAC,CAAC;;MAEjB;MACAnB,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KACpCA,KAAK,KAAKC,CAAC,GAAG;QAAE,GAAGjB,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,GAAGI,IACjD,CAAC,CAAC;;MAEF;MACA,MAAM,IAAImB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEvB,KAAK,CAACoB,CAAC,CAAC,CAACtB,QAAQ,CAAC,CAAC;;MAEpE;MACA,MAAM2B,UAAU,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,KAAKP,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC;MAE9D,IAAIK,UAAU,EAAE;QACd;QACAxB,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KACpCA,KAAK,KAAKC,CAAC,GAAG;UAAE,GAAGjB,IAAI;UAAEJ,MAAM,EAAE;QAAS,CAAC,GAAGI,IAChD,CAAC,CAAC;QACFhB,iBAAiB,CAAC,QAAQ,CAAC;QAC3BE,aAAa,CAAC+B,CAAC,CAAC;QAChBnC,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;;MAEA;MACAgB,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KACpCA,KAAK,KAAKC,CAAC,GAAG;QAAE,GAAGjB,IAAI;QAAEJ,MAAM,EAAE;MAAY,CAAC,GAAGI,IACnD,CAAC,CAAC;;MAEF;MACA,MAAM,IAAImB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACxD;;IAEA;IACA,MAAMf,MAAM,GAAGJ,uBAAuB,CAAC,CAAC;IACxCb,mBAAmB,CAACiB,MAAM,CAAC;IAC3BrB,iBAAiB,CAAC,WAAW,CAAC;IAC9BE,aAAa,CAAC,IAAI,CAAC;IACnBJ,YAAY,CAAC,KAAK,CAAC;IACnBF,cAAc,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAM6C,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIxC,UAAU,KAAK,IAAI,IAAI,CAACJ,SAAS,EAAE;MACrC+B,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1B5C,YAAY,CAAC,KAAK,CAAC;IACnBE,iBAAiB,CAAC,MAAM,CAAC;IACzBJ,cAAc,CAAC,CAAC,CAAC,CAAC;IAClBM,aAAa,CAAC,IAAI,CAAC;IACnBE,mBAAmB,CAAC,IAAI,CAAC;IACzBU,QAAQ,CAACT,aAAa,CAAC0B,GAAG,CAACf,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEJ,MAAM,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,oBACEpB,OAAA;IAAKmD,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAErBpD,OAAA;MAAKmD,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBAExGpD,OAAA;QAAKmD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCpD,OAAA;UAAKmD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpD,OAAA;YAAKmD,SAAS,EAAC,mEAAmE;YAAAC,QAAA,eAChFpD,OAAA;cAAMmD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNxD,OAAA;YAAAoD,QAAA,gBACEpD,OAAA;cAAImD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CxD,OAAA;cAAGmD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxD,OAAA;QAAKmD,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAElBpD,OAAA;UAAKmD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBpD,OAAA;YAAKmD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CpD,OAAA;cAAGmD,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/CxD,OAAA;cAAKmD,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEpD,OAAA;gBAAMmD,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,2HAC5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxD,OAAA;UAAKmD,SAAS,EAAC,UAAU;UAAAC,QAAA,EACtB/B,KAAK,CAACa,KAAK,CAAC,CAAC,EAAEuB,YAAY,CAAC,CAAClB,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KAAK;YACjD,MAAMkB,QAAQ,GAAGrC,KAAK,CAACmB,KAAK,GAAG,CAAC,CAAC;YACjC,MAAMmB,mBAAmB,GAAGnB,KAAK,GAAGiB,YAAY,GAAG,CAAC,IAAIjB,KAAK,GAAGnB,KAAK,CAACqB,MAAM,GAAG,CAAC;YAChF,MAAMkB,cAAc,GAAGpC,IAAI,CAACJ,MAAM,KAAK,WAAW,GAAG,cAAc,GAAG,aAAa;YAEnF,oBACEpB,OAAA;cAAmBmD,SAAS,EAAC,UAAU;cAAAC,QAAA,GAEpCO,mBAAmB,iBAClB3D,OAAA;gBACEmD,SAAS,EAAE,+DAA+DS,cAAc,EAAG;gBAC3FC,KAAK,EAAE;kBACLC,MAAM,EAAE,MAAM,CAAC;gBACjB;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP,eAGDxD,OAAA;gBAAKmD,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAE5DpD,OAAA;kBAAKmD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBAErCpD,OAAA;oBAAKmD,SAAS,EAAC;kBAA4E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGlGxD,OAAA;oBAAKmD,SAAS,EAAE,6GACd3B,IAAI,CAACJ,MAAM,KAAK,WAAW,GAAG,yBAAyB,GACvDI,IAAI,CAACJ,MAAM,KAAK,QAAQ,GAAG,uBAAuB,GAClDI,IAAI,CAACJ,MAAM,KAAK,SAAS,GAAG,wBAAwB,GACpD,2BAA2B,EAC1B;oBAAAgC,QAAA,GACA5B,IAAI,CAACJ,MAAM,KAAK,WAAW,iBAAIpB,OAAA,CAACN,WAAW;sBAACyD,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAClEhC,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBAAIpB,OAAA,CAACL,OAAO;sBAACwD,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC3DhC,IAAI,CAACJ,MAAM,KAAK,SAAS,iBAAIpB,OAAA,CAACH,OAAO;sBAACsD,SAAS,EAAC;oBAAsB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACzEhC,IAAI,CAACJ,MAAM,KAAK,SAAS,iBAAIpB,OAAA;sBAAKmD,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGRxD,OAAA;kBAAKmD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCpD,OAAA;oBAAKmD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBpD,OAAA;sBAAImD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAE5B,IAAI,CAACT;oBAAI;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAEjEhC,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBACvBpB,OAAA;sBACE+D,OAAO,EAAEd,eAAgB;sBACzBe,QAAQ,EAAE3D,SAAU;sBACpB8C,SAAS,EAAC,2TAA2T;sBAAAC,QAAA,gBAErUpD,OAAA,CAACP,SAAS;wBAAC0D,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjCxD,OAAA;wBAAAoD,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAENxD,OAAA;oBAAGmD,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACjD7B,kBAAkB,CAACC,IAAI;kBAAC;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,EAGHhC,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBACvBpB,OAAA;oBAAKmD,SAAS,EAAC,qDAAqD;oBAAAC,QAAA,eAClEpD,OAAA;sBAAGmD,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAAC;oBAEpC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA9DIhC,IAAI,CAACV,EAAE;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+Dd,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNxD,OAAA;UAAKmD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDpD,OAAA;YAAKmD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpD,OAAA;cAAKmD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GACzC7C,cAAc,KAAK,MAAM,iBACxBP,OAAA;gBACE+D,OAAO,EAAE3B,eAAgB;gBACzB4B,QAAQ,EAAE3D,SAAU;gBACpB8C,SAAS,EAAC,0SAA0S;gBAAAC,QAAA,gBAEpTpD,OAAA,CAACR,IAAI;kBAAC2D,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BxD,OAAA;kBAAAoD,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT,EAEAjD,cAAc,KAAK,SAAS,iBAC3BP,OAAA;gBACEgE,QAAQ;gBACRb,SAAS,EAAC,+IAA+I;gBAAAC,QAAA,gBAEzJpD,OAAA,CAACH,OAAO;kBAACsD,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CxD,OAAA;kBAAAoD,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACT,EAEAjD,cAAc,KAAK,WAAW,iBAC7BP,OAAA;gBACE+D,OAAO,EAAEb,aAAc;gBACvBC,SAAS,EAAC,8PAA8P;gBAAAC,QAAA,gBAExQpD,OAAA,CAACN,WAAW;kBAACyD,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnCxD,OAAA;kBAAAoD,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT,EAEAjD,cAAc,KAAK,QAAQ,iBAC1BP,OAAA;gBAAKmD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CpD,OAAA;kBACE+D,OAAO,EAAEd,eAAgB;kBACzBe,QAAQ,EAAE3D,SAAU;kBACpB8C,SAAS,EAAC,kTAAkT;kBAAAC,QAAA,gBAE5TpD,OAAA,CAACP,SAAS;oBAAC0D,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjCxD,OAAA;oBAAAoD,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACTxD,OAAA;kBACE+D,OAAO,EAAEb,aAAc;kBACvBC,SAAS,EAAC,8NAA8N;kBAAAC,QAAA,EACzO;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAGAjD,cAAc,KAAK,SAAS,iBAC3BP,OAAA;gBAAKmD,SAAS,EAAC,+HAA+H;gBAAAC,QAAA,gBAC5IpD,OAAA;kBAAKmD,SAAS,EAAC,kGAAkG;kBAAAC,QAAA,eAC/GpD,OAAA,CAACH,OAAO;oBAACsD,SAAS,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACNxD,OAAA;kBAAMmD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,iCAC5C,EAACjD,WAAW,GAAG,CAAC,EAAC,sBAAK,EAACkB,KAAK,CAACqB,MAAM,EAAC,SAC5C;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNxD,OAAA;cACE+D,OAAO,EAAEb,aAAc;cACvBC,SAAS,EAAC,gHAAgH;cAC1Hc,KAAK,EAAC,0BAAM;cAAAb,QAAA,eAEZpD,OAAA,CAACP,SAAS;gBAAC0D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLjD,cAAc,KAAK,WAAW,iBAC7BP,OAAA;YAAKmD,SAAS,EAAC,uGAAuG;YAAAC,QAAA,eACpHpD,OAAA;cAAKmD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CpD,OAAA;gBAAKmD,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,eAC/HpD,OAAA,CAACN,WAAW;kBAACyD,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNxD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAKmD,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,EAAC;gBAE7D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxD,OAAA;kBAAKmD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAExC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAjD,cAAc,KAAK,QAAQ,IAAIE,UAAU,KAAK,IAAI,iBACjDT,OAAA;YAAKmD,SAAS,EAAC,kGAAkG;YAAAC,QAAA,eAC/GpD,OAAA;cAAKmD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CpD,OAAA;gBAAKmD,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,eAC5HpD,OAAA,CAACJ,WAAW;kBAACuD,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNxD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAKmD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxD,OAAA;kBAAKmD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAC,SAClC,EAAC3C,UAAU,GAAG,CAAC,EAAC,6FACpB;gBAAA;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAKmD,SAAS,EAAC,iGAAiG;MAAAC,QAAA,gBAC9GpD,OAAA;QAAImD,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAChEpD,OAAA;UAAKmD,SAAS,EAAC;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gCAE/D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLxD,OAAA;QAAGmD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACvDpD,OAAA;UAAMmD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CxD,OAAA;UAAMmD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACvDpD,OAAA;YAAMmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAACpD,OAAA,CAACN,WAAW;cAACyD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtGxD,OAAA;YAAMmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAACpD,OAAA,CAACN,WAAW;cAACyD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAAG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrGxD,OAAA;YAAMmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAACpD,OAAA,CAACN,WAAW;cAACyD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtGxD,OAAA;YAAMmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAACpD,OAAA,CAACN,WAAW;cAACyD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACJxD,OAAA;QAAKmD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCpD,OAAA;UACE+D,OAAO,EAAE3B,eAAgB;UACzB4B,QAAQ,EAAE3D,SAAU;UACpB8C,SAAS,EAAC,qRAAqR;UAAAC,QAAA,gBAE/RpD,OAAA;YAAAoD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfxD,OAAA;YAAAoD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACTxD,OAAA;UACE+D,OAAO,EAAEb,aAAc;UACvBC,SAAS,EAAC,qOAAqO;UAAAC,QAAA,gBAE/OpD,OAAA;YAAAoD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfxD,OAAA;YAAAoD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACTxD,OAAA;UACE+D,OAAO,EAAEA,CAAA,KAAM;YACb;YACAG,eAAe,CAAC,CAAC,CAAC;YAClB5C,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KAAK;cACzC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGhB,IAAI;gBAAEJ,MAAM,EAAE;cAAY,CAAC;cACxD,IAAIoB,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGhB,IAAI;gBAAEJ,MAAM,EAAE;cAAY,CAAC;cACxD,IAAIoB,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGhB,IAAI;gBAAEJ,MAAM,EAAE;cAAS,CAAC;cACrD,OAAOI,IAAI;YACb,CAAC,CAAC,CAAC;YACHhB,iBAAiB,CAAC,QAAQ,CAAC;YAC3BE,aAAa,CAAC,CAAC,CAAC;UAClB,CAAE;UACFyC,SAAS,EAAC,6OAA6O;UAAAC,QAAA,gBAEvPpD,OAAA;YAAAoD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfxD,OAAA;YAAAoD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA/bID,gBAAgB;AAAAkE,EAAA,GAAhBlE,gBAAgB;AAictB,eAAeA,gBAAgB;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}