{"ast": null, "code": "import React from'react';import TemuWorkflowDemo from'./TemuWorkflowDemo';import{jsx as _jsx}from\"react/jsx-runtime\";const TemuWorkflowShowcase=()=>{return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-4xl mx-auto\",children:/*#__PURE__*/_jsx(TemuWorkflowDemo,{})})});};export default TemuWorkflowShowcase;", "map": {"version": 3, "names": ["React", "TemuWorkflowDemo", "jsx", "_jsx", "TemuWorkflowShowcase", "className", "children"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx"], "sourcesContent": ["import React from 'react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\n\nconst TemuWorkflowShowcase = () => {\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6\">\n      <div className=\"max-w-4xl mx-auto\">\n        <TemuWorkflowDemo />\n      </div>\n    </div>\n  );\n};\n\n\nexport default TemuWorkflowShowcase;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,gBAAgB,KAAM,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAElD,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CAEjC,mBACED,IAAA,QAAKE,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFH,IAAA,QAAKE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChCH,IAAA,CAACF,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACH,CAAC,CAEV,CAAC,CAGD,cAAe,CAAAG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}