{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Regex = createLucideIcon(\"Regex\", [[\"path\", {\n  d: \"M17 3v10\",\n  key: \"15fgeh\"\n}], [\"path\", {\n  d: \"m12.67 5.5 8.66 5\",\n  key: \"1gpheq\"\n}], [\"path\", {\n  d: \"m12.67 10.5 8.66-5\",\n  key: \"1dkfa6\"\n}], [\"path\", {\n  d: \"M9 17a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2v-2z\",\n  key: \"swwfx4\"\n}]]);\nexport { Regex as default };", "map": {"version": 3, "names": ["Regex", "createLucideIcon", "d", "key"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/node_modules/lucide-react/src/icons/regex.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Regex\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgM3YxMCIgLz4KICA8cGF0aCBkPSJtMTIuNjcgNS41IDguNjYgNSIgLz4KICA8cGF0aCBkPSJtMTIuNjcgMTAuNSA4LjY2LTUiIC8+CiAgPHBhdGggZD0iTTkgMTdhMiAyIDAgMCAwLTItMkg1YTIgMiAwIDAgMC0yIDJ2MmEyIDIgMCAwIDAgMiAyaDJhMiAyIDAgMCAwIDItMnYtMnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/regex\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Regex = createLucideIcon('Regex', [\n  ['path', { d: 'M17 3v10', key: '15fgeh' }],\n  ['path', { d: 'm12.67 5.5 8.66 5', key: '1gpheq' }],\n  ['path', { d: 'm12.67 10.5 8.66-5', key: '1dkfa6' }],\n  [\n    'path',\n    {\n      d: 'M9 17a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2v-2z',\n      key: 'swwfx4',\n    },\n  ],\n]);\n\nexport default Regex;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}