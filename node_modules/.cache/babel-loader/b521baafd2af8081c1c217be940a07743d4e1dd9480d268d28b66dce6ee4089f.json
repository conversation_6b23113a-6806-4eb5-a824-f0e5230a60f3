{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst SplitSquareHorizontal = createLucideIcon(\"SplitSquareHorizontal\", [[\"path\", {\n  d: \"M8 19H5c-1 0-2-1-2-2V7c0-1 1-2 2-2h3\",\n  key: \"lubmu8\"\n}], [\"path\", {\n  d: \"M16 5h3c1 0 2 1 2 2v10c0 1-1 2-2 2h-3\",\n  key: \"1ag34g\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"4\",\n  y2: \"20\",\n  key: \"1tx1rr\"\n}]]);\nexport { SplitSquareHorizontal as default };", "map": {"version": 3, "names": ["SplitSquareHorizontal", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/node_modules/lucide-react/src/icons/split-square-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SplitSquareHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAxOUg1Yy0xIDAtMi0xLTItMlY3YzAtMSAxLTIgMi0yaDMiIC8+CiAgPHBhdGggZD0iTTE2IDVoM2MxIDAgMiAxIDIgMnYxMGMwIDEtMSAyLTIgMmgtMyIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjQiIHkyPSIyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/split-square-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SplitSquareHorizontal = createLucideIcon('SplitSquareHorizontal', [\n  ['path', { d: 'M8 19H5c-1 0-2-1-2-2V7c0-1 1-2 2-2h3', key: 'lubmu8' }],\n  ['path', { d: 'M16 5h3c1 0 2 1 2 2v10c0 1-1 2-2 2h-3', key: '1ag34g' }],\n  ['line', { x1: '12', x2: '12', y1: '4', y2: '20', key: '1tx1rr' }],\n]);\n\nexport default SplitSquareHorizontal;\n"], "mappings": ";;;;;AAaM,MAAAA,qBAAA,GAAwBC,gBAAA,CAAiB,uBAAyB,GACtE,CAAC,MAAQ;EAAEC,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}