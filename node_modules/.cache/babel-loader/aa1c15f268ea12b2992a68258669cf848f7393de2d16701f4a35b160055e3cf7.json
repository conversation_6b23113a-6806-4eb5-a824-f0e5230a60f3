{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx\";\nimport React from 'react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowShowcase = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 left-1/4 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-1/4 w-72 h-72 bg-purple-400/20 rounded-full blur-3xl\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-24\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), \"AI\\u9A71\\u52A8\\u7684\\u7535\\u5546\\u81EA\\u52A8\\u5316\\u89E3\\u51B3\\u65B9\\u6848\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n              children: \"Temu\\u6D41\\u91CF\\u52A0\\u901F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-800\",\n              children: \"\\u81EA\\u52A8\\u5316\\u5DE5\\u4F5C\\u6D41\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed\",\n            children: \"\\u4F53\\u9A8C\\u667A\\u80FD\\u5316\\u7684Temu\\u5546\\u54C1\\u6D41\\u91CF\\u52A0\\u901F\\u914D\\u7F6E\\u6D41\\u7A0B\\uFF0C\\u4ECE\\u767B\\u5F55\\u9A8C\\u8BC1\\u5230\\u5546\\u54C1\\u7B5B\\u9009\\uFF0C\\u518D\\u5230\\u6D41\\u91CF\\u52A0\\u6743\\u8BBE\\u7F6E\\uFF0C \\u5168\\u7A0B\\u81EA\\u52A8\\u5316\\u6267\\u884C\\uFF0C\\u8BA9\\u60A8\\u7684\\u7535\\u5546\\u8FD0\\u8425\\u66F4\\u52A0\\u9AD8\\u6548\\u4FBF\\u6377\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto\",\n            children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center mb-2 text-blue-600\",\n                children: stat.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"group\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`,\n                children: feature.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm leading-relaxed\",\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/80 backdrop-blur-sm rounded-xl p-1 shadow-lg border border-white/20\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('demo'),\n              className: `px-6 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === 'demo' ? 'bg-blue-500 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(Play, {\n                className: \"w-4 h-4 inline mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), \"\\u5B9E\\u65F6\\u6F14\\u793A\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveTab('features'),\n              className: `px-6 py-3 rounded-lg font-medium transition-all duration-200 ${activeTab === 'features' ? 'bg-blue-500 text-white shadow-lg' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'}`,\n              children: [/*#__PURE__*/_jsxDEV(Star, {\n                className: \"w-4 h-4 inline mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this), \"\\u529F\\u80FD\\u7279\\u6027\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), activeTab === 'demo' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: /*#__PURE__*/_jsxDEV(TemuWorkflowDemo, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Target, {\n                className: \"w-5 h-5 mr-2 text-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this), \"\\u64CD\\u4F5C\\u6307\\u5357\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\",\n                  children: \"1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u70B9\\u51FB\\\"\\u5F00\\u59CB\\u6267\\u884C\\\"\\u6309\\u94AE\\u542F\\u52A8\\u81EA\\u52A8\\u5316\\u6D41\\u7A0B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\",\n                  children: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u89C2\\u5BDF\\u6BCF\\u4E2A\\u6B65\\u9AA4\\u7684\\u5B9E\\u65F6\\u6267\\u884C\\u72B6\\u6001\\u548C\\u63CF\\u8FF0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\",\n                  children: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u5982\\u9047\\u5931\\u8D25\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\",\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"\\u4F7F\\u7528Demo\\u63A7\\u5236\\u9762\\u677F\\u4F53\\u9A8C\\u4E0D\\u540C\\u573A\\u666F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900 mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                className: \"w-5 h-5 mr-2 text-purple-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), \"\\u529F\\u80FD\\u4EAE\\u70B9\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: highlights.map((highlight, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-purple-500 mt-0.5\",\n                  children: highlight.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium text-gray-900 text-sm\",\n                    children: highlight.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600 mt-1\",\n                    children: highlight.detail\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                className: \"w-5 h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this), \"\\u9002\\u7528\\u573A\\u666F\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mr-2 opacity-70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), \"\\u5927\\u6279\\u91CF\\u5546\\u54C1\\u6D41\\u91CF\\u52A0\\u901F\\u914D\\u7F6E\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mr-2 opacity-70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this), \"\\u5B9A\\u671F\\u8425\\u9500\\u6D3B\\u52A8\\u81EA\\u52A8\\u5316\\u6267\\u884C\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mr-2 opacity-70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this), \"\\u591A\\u5E97\\u94FA\\u7EDF\\u4E00\\u8FD0\\u8425\\u7BA1\\u7406\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(ArrowRight, {\n                  className: \"w-4 h-4 mr-2 opacity-70\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this), \"\\u7535\\u5546\\u8FD0\\u8425\\u6548\\u7387\\u63D0\\u5347\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 11\n      }, this), activeTab === 'features' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-8 text-center\",\n            children: \"\\u6838\\u5FC3\\u529F\\u80FD\\u8BE6\\u89E3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Zap, {\n                  className: \"w-6 h-6 mr-3 text-blue-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this), \"\\u81EA\\u52A8\\u5316\\u5DE5\\u4F5C\\u6D41\\u7A0B\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid md:grid-cols-2 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 rounded-xl p-4 border border-blue-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\",\n                        children: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 203,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"\\u667A\\u80FD\\u767B\\u5F55\\u9A8C\\u8BC1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 ml-11\",\n                      children: \"\\u81EA\\u52A8\\u9A8C\\u8BC1Temu\\u8D26\\u53F7\\u4FE1\\u606F\\uFF0C\\u786E\\u4FDD\\u5B89\\u5168\\u767B\\u5F55\\u5E76\\u83B7\\u53D6\\u5E97\\u94FA\\u6743\\u9650\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-purple-50 rounded-xl p-4 border border-purple-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\",\n                        children: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 211,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"\\u7CBE\\u51C6\\u5546\\u54C1\\u7B5B\\u9009\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 212,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 ml-11\",\n                      children: \"\\u6839\\u636E\\u8BBE\\u5B9A\\u6761\\u4EF6\\u667A\\u80FD\\u7B5B\\u9009\\u7B26\\u5408\\u8981\\u6C42\\u7684\\u5546\\u54C1\\uFF0C\\u63D0\\u9AD8\\u6295\\u653E\\u7CBE\\u51C6\\u5EA6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-50 rounded-xl p-4 border border-green-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\",\n                        children: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 221,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"\\u6D41\\u91CF\\u52A0\\u6743\\u914D\\u7F6E\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 ml-11\",\n                      children: \"\\u4E3A\\u7B5B\\u9009\\u51FA\\u7684\\u5546\\u54C1\\u81EA\\u52A8\\u8BBE\\u7F6E\\u6D41\\u91CF\\u52A0\\u901F\\u6743\\u91CD\\uFF0C\\u4F18\\u5316\\u66DD\\u5149\\u6548\\u679C\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 224,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-orange-50 rounded-xl p-4 border border-orange-100\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\",\n                        children: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-gray-900\",\n                        children: \"\\u6267\\u884C\\u7ED3\\u679C\\u7EDF\\u8BA1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 230,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 ml-11\",\n                      children: \"\\u751F\\u6210\\u8BE6\\u7EC6\\u7684\\u6267\\u884C\\u62A5\\u544A\\uFF0C\\u5305\\u542B\\u6210\\u529F\\u6570\\u91CF\\u3001\\u8D39\\u7528\\u7EDF\\u8BA1\\u7B49\\u4FE1\\u606F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"border-t border-gray-200 pt-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Shield, {\n                  className: \"w-6 h-6 mr-3 text-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this), \"\\u6280\\u672F\\u7279\\u6027\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid md:grid-cols-3 gap-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-8 h-8 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900 mb-2\",\n                    children: \"\\u5B9E\\u65F6\\u72B6\\u6001\\u76D1\\u63A7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u6BCF\\u4E2A\\u6B65\\u9AA4\\u90FD\\u6709\\u8BE6\\u7EC6\\u7684\\u72B6\\u6001\\u53CD\\u9988\\u548C\\u8FDB\\u5EA6\\u663E\\u793A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                      className: \"w-8 h-8 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900 mb-2\",\n                    children: \"\\u667A\\u80FD\\u9519\\u8BEF\\u6062\\u590D\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u81EA\\u52A8\\u68C0\\u6D4B\\u6267\\u884C\\u5931\\u8D25\\u5E76\\u652F\\u6301\\u4E00\\u952E\\u91CD\\u8BD5\\u529F\\u80FD\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-8 h-8 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"font-semibold text-gray-900 mb-2\",\n                    children: \"\\u4F18\\u96C5\\u7528\\u6237\\u4F53\\u9A8C\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"\\u6D41\\u7545\\u7684\\u52A8\\u753B\\u6548\\u679C\\u548C\\u76F4\\u89C2\\u7684\\u754C\\u9762\\u8BBE\\u8BA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = TemuWorkflowShowcase;\nexport default TemuWorkflowShowcase;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowShowcase\");", "map": {"version": 3, "names": ["React", "TemuWorkflowDemo", "jsxDEV", "_jsxDEV", "TemuWorkflowShowcase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON><PERSON>", "stats", "map", "stat", "index", "icon", "value", "label", "features", "feature", "color", "title", "description", "onClick", "setActiveTab", "activeTab", "Play", "Star", "Target", "highlights", "highlight", "text", "detail", "TrendingUp", "ArrowRight", "Zap", "Shield", "Loader2", "RefreshCw", "CheckCircle", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx"], "sourcesContent": ["import React from 'react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\n\nconst TemuWorkflowShowcase = () => {\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\">\n      {/* 头部区域 */}\n      <div className=\"relative overflow-hidden\">\n        {/* 背景装饰 */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10\"></div>\n        <div className=\"absolute top-0 left-1/4 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-0 right-1/4 w-72 h-72 bg-purple-400/20 rounded-full blur-3xl\"></div>\n        \n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-24\">\n          {/* 主标题区域 */}\n          <div className=\"text-center mb-16\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6\">\n              <Sparkles className=\"w-4 h-4 mr-2\" />\n              AI驱动的电商自动化解决方案\n            </div>\n            \n            <h1 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n              <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                Temu流量加速\n              </span>\n              <br />\n              <span className=\"text-gray-800\">自动化工作流</span>\n            </h1>\n            \n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed\">\n              体验智能化的Temu商品流量加速配置流程，从登录验证到商品筛选，再到流量加权设置，\n              全程自动化执行，让您的电商运营更加高效便捷。\n            </p>\n\n            {/* 统计数据 */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto\">\n              {stats.map((stat, index) => (\n                <div key={index} className=\"bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20\">\n                  <div className=\"flex items-center justify-center mb-2 text-blue-600\">\n                    {stat.icon}\n                  </div>\n                  <div className=\"text-2xl font-bold text-gray-900\">{stat.value}</div>\n                  <div className=\"text-sm text-gray-600\">{stat.label}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* 功能特性卡片 */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n            {features.map((feature, index) => (\n              <div key={index} className=\"group\">\n                <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\n                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                    {feature.icon}\n                  </div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{feature.title}</h3>\n                  <p className=\"text-gray-600 text-sm leading-relaxed\">{feature.description}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        {/* 标签页导航 */}\n        <div className=\"flex justify-center mb-8\">\n          <div className=\"bg-white/80 backdrop-blur-sm rounded-xl p-1 shadow-lg border border-white/20\">\n            <div className=\"flex space-x-1\">\n              <button\n                onClick={() => setActiveTab('demo')}\n                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${\n                  activeTab === 'demo'\n                    ? 'bg-blue-500 text-white shadow-lg'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                }`}\n              >\n                <Play className=\"w-4 h-4 inline mr-2\" />\n                实时演示\n              </button>\n              <button\n                onClick={() => setActiveTab('features')}\n                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${\n                  activeTab === 'features'\n                    ? 'bg-blue-500 text-white shadow-lg'\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                }`}\n              >\n                <Star className=\"w-4 h-4 inline mr-2\" />\n                功能特性\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 内容区域 */}\n        {activeTab === 'demo' && (\n          <div className=\"grid lg:grid-cols-3 gap-8\">\n            {/* 演示区域 */}\n            <div className=\"lg:col-span-2\">\n              <TemuWorkflowDemo />\n            </div>\n\n            {/* 侧边栏信息 */}\n            <div className=\"space-y-6\">\n              {/* 操作指南 */}\n              <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <Target className=\"w-5 h-5 mr-2 text-blue-500\" />\n                  操作指南\n                </h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\">1</div>\n                    <p className=\"text-sm text-gray-600\">点击\"开始执行\"按钮启动自动化流程</p>\n                  </div>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\">2</div>\n                    <p className=\"text-sm text-gray-600\">观察每个步骤的实时执行状态和描述</p>\n                  </div>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\">3</div>\n                    <p className=\"text-sm text-gray-600\">如遇失败可点击重试继续执行</p>\n                  </div>\n                  <div className=\"flex items-start space-x-3\">\n                    <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5\">4</div>\n                    <p className=\"text-sm text-gray-600\">使用Demo控制面板体验不同场景</p>\n                  </div>\n                </div>\n              </div>\n\n              {/* 功能亮点 */}\n              <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <Sparkles className=\"w-5 h-5 mr-2 text-purple-500\" />\n                  功能亮点\n                </h3>\n                <div className=\"space-y-4\">\n                  {highlights.map((highlight, index) => (\n                    <div key={index} className=\"flex items-start space-x-3\">\n                      <div className=\"text-purple-500 mt-0.5\">\n                        {highlight.icon}\n                      </div>\n                      <div>\n                        <div className=\"font-medium text-gray-900 text-sm\">{highlight.text}</div>\n                        <div className=\"text-xs text-gray-600 mt-1\">{highlight.detail}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* 使用场景 */}\n              <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white shadow-lg\">\n                <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n                  <TrendingUp className=\"w-5 h-5 mr-2\" />\n                  适用场景\n                </h3>\n                <ul className=\"space-y-2 text-sm\">\n                  <li className=\"flex items-center\">\n                    <ArrowRight className=\"w-4 h-4 mr-2 opacity-70\" />\n                    大批量商品流量加速配置\n                  </li>\n                  <li className=\"flex items-center\">\n                    <ArrowRight className=\"w-4 h-4 mr-2 opacity-70\" />\n                    定期营销活动自动化执行\n                  </li>\n                  <li className=\"flex items-center\">\n                    <ArrowRight className=\"w-4 h-4 mr-2 opacity-70\" />\n                    多店铺统一运营管理\n                  </li>\n                  <li className=\"flex items-center\">\n                    <ArrowRight className=\"w-4 h-4 mr-2 opacity-70\" />\n                    电商运营效率提升\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'features' && (\n          <div className=\"max-w-4xl mx-auto\">\n            {/* 详细功能介绍 */}\n            <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">核心功能详解</h2>\n              \n              <div className=\"space-y-8\">\n                {/* 工作流步骤详解 */}\n                <div>\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n                    <Zap className=\"w-6 h-6 mr-3 text-blue-500\" />\n                    自动化工作流程\n                  </h3>\n                  \n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-blue-50 rounded-xl p-4 border border-blue-100\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\">1</div>\n                          <h4 className=\"font-semibold text-gray-900\">智能登录验证</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 ml-11\">自动验证Temu账号信息，确保安全登录并获取店铺权限</p>\n                      </div>\n                      \n                      <div className=\"bg-purple-50 rounded-xl p-4 border border-purple-100\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\">2</div>\n                          <h4 className=\"font-semibold text-gray-900\">精准商品筛选</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 ml-11\">根据设定条件智能筛选符合要求的商品，提高投放精准度</p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"space-y-4\">\n                      <div className=\"bg-green-50 rounded-xl p-4 border border-green-100\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\">3</div>\n                          <h4 className=\"font-semibold text-gray-900\">流量加权配置</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 ml-11\">为筛选出的商品自动设置流量加速权重，优化曝光效果</p>\n                      </div>\n                      \n                      <div className=\"bg-orange-50 rounded-xl p-4 border border-orange-100\">\n                        <div className=\"flex items-center mb-2\">\n                          <div className=\"w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3\">4</div>\n                          <h4 className=\"font-semibold text-gray-900\">执行结果统计</h4>\n                        </div>\n                        <p className=\"text-sm text-gray-600 ml-11\">生成详细的执行报告，包含成功数量、费用统计等信息</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* 技术特性 */}\n                <div className=\"border-t border-gray-200 pt-8\">\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-6 flex items-center\">\n                    <Shield className=\"w-6 h-6 mr-3 text-green-500\" />\n                    技术特性\n                  </h3>\n                  \n                  <div className=\"grid md:grid-cols-3 gap-6\">\n                    <div className=\"text-center\">\n                      <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                        <Loader2 className=\"w-8 h-8 text-white\" />\n                      </div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">实时状态监控</h4>\n                      <p className=\"text-sm text-gray-600\">每个步骤都有详细的状态反馈和进度显示</p>\n                    </div>\n                    \n                    <div className=\"text-center\">\n                      <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                        <RefreshCw className=\"w-8 h-8 text-white\" />\n                      </div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">智能错误恢复</h4>\n                      <p className=\"text-sm text-gray-600\">自动检测执行失败并支持一键重试功能</p>\n                    </div>\n                    \n                    <div className=\"text-center\">\n                      <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4\">\n                        <CheckCircle className=\"w-8 h-8 text-white\" />\n                      </div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">优雅用户体验</h4>\n                      <p className=\"text-sm text-gray-600\">流畅的动画效果和直观的界面设计</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TemuWorkflowShowcase;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAEjC,oBACED,OAAA;IAAKE,SAAS,EAAC,wEAAwE;IAAAC,QAAA,gBAErFH,OAAA;MAAKE,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBAEvCH,OAAA;QAAKE,SAAS,EAAC;MAAqE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3FP,OAAA;QAAKE,SAAS,EAAC;MAAwE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9FP,OAAA;QAAKE,SAAS,EAAC;MAA2E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEjGP,OAAA;QAAKE,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAE1EH,OAAA;UAAKE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCH,OAAA;YAAKE,SAAS,EAAC,oGAAoG;YAAAC,QAAA,gBACjHH,OAAA,CAACQ,QAAQ;cAACN,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,8EAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENP,OAAA;YAAIE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAC/DH,OAAA;cAAME,SAAS,EAAC,4EAA4E;cAAAC,QAAA,EAAC;YAE7F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPP,OAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNP,OAAA;cAAME,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAELP,OAAA;YAAGE,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EAAC;UAG5E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJP,OAAA;YAAKE,SAAS,EAAC,yDAAyD;YAAAC,QAAA,EACrEM,KAAK,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBZ,OAAA;cAAiBE,SAAS,EAAC,8EAA8E;cAAAC,QAAA,gBACvGH,OAAA;gBAAKE,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EACjEQ,IAAI,CAACE;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEQ,IAAI,CAACG;cAAK;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpEP,OAAA;gBAAKE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEQ,IAAI,CAACI;cAAK;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GALjDK,KAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNP,OAAA;UAAKE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,EAC5Da,QAAQ,CAACN,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBAC3BZ,OAAA;YAAiBE,SAAS,EAAC,OAAO;YAAAC,QAAA,eAChCH,OAAA;cAAKE,SAAS,EAAC,gJAAgJ;cAAAC,QAAA,gBAC7JH,OAAA;gBAAKE,SAAS,EAAE,yCAAyCe,OAAO,CAACC,KAAK,2GAA4G;gBAAAf,QAAA,EAC/Kc,OAAO,CAACJ;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNP,OAAA;gBAAIE,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAEc,OAAO,CAACE;cAAK;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7EP,OAAA;gBAAGE,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEc,OAAO,CAACG;cAAW;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UAAC,GAPEK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNP,OAAA;MAAKE,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAE3DH,OAAA;QAAKE,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eACvCH,OAAA;UAAKE,SAAS,EAAC,8EAA8E;UAAAC,QAAA,eAC3FH,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BH,OAAA;cACEqB,OAAO,EAAEA,CAAA,KAAMC,YAAY,CAAC,MAAM,CAAE;cACpCpB,SAAS,EAAE,gEACTqB,SAAS,KAAK,MAAM,GAChB,kCAAkC,GAClC,oDAAoD,EACvD;cAAApB,QAAA,gBAEHH,OAAA,CAACwB,IAAI;gBAACtB,SAAS,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTP,OAAA;cACEqB,OAAO,EAAEA,CAAA,KAAMC,YAAY,CAAC,UAAU,CAAE;cACxCpB,SAAS,EAAE,gEACTqB,SAAS,KAAK,UAAU,GACpB,kCAAkC,GAClC,oDAAoD,EACvD;cAAApB,QAAA,gBAEHH,OAAA,CAACyB,IAAI;gBAACvB,SAAS,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLgB,SAAS,KAAK,MAAM,iBACnBvB,OAAA;QAAKE,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBAExCH,OAAA;UAAKE,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BH,OAAA,CAACF,gBAAgB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAGNP,OAAA;UAAKE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBH,OAAA;YAAKE,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5FH,OAAA;cAAIE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxEH,OAAA,CAAC0B,MAAM;gBAACxB,SAAS,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEnD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLP,OAAA;cAAKE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBH,OAAA;gBAAKE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCH,OAAA;kBAAKE,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJP,OAAA;kBAAGE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCH,OAAA;kBAAKE,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJP,OAAA;kBAAGE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCH,OAAA;kBAAKE,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJP,OAAA;kBAAGE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCH,OAAA;kBAAKE,SAAS,EAAC,0HAA0H;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjJP,OAAA;kBAAGE,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNP,OAAA;YAAKE,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5FH,OAAA;cAAIE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxEH,OAAA,CAACQ,QAAQ;gBAACN,SAAS,EAAC;cAA8B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLP,OAAA;cAAKE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBwB,UAAU,CAACjB,GAAG,CAAC,CAACkB,SAAS,EAAEhB,KAAK,kBAC/BZ,OAAA;gBAAiBE,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACrDH,OAAA;kBAAKE,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EACpCyB,SAAS,CAACf;gBAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACNP,OAAA;kBAAAG,QAAA,gBACEH,OAAA;oBAAKE,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEyB,SAAS,CAACC;kBAAI;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzEP,OAAA;oBAAKE,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAEyB,SAAS,CAACE;kBAAM;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC;cAAA,GAPEK,KAAK;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNP,OAAA;YAAKE,SAAS,EAAC,mFAAmF;YAAAC,QAAA,gBAChGH,OAAA;cAAIE,SAAS,EAAC,8CAA8C;cAAAC,QAAA,gBAC1DH,OAAA,CAAC+B,UAAU;gBAAC7B,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLP,OAAA;cAAIE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BH,OAAA;gBAAIE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BH,OAAA,CAACgC,UAAU;kBAAC9B,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sEAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLP,OAAA;gBAAIE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BH,OAAA,CAACgC,UAAU;kBAAC9B,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sEAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLP,OAAA;gBAAIE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BH,OAAA,CAACgC,UAAU;kBAAC9B,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,0DAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLP,OAAA;gBAAIE,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/BH,OAAA,CAACgC,UAAU;kBAAC9B,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oDAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAgB,SAAS,KAAK,UAAU,iBACvBvB,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAEhCH,OAAA;UAAKE,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAC5FH,OAAA;YAAIE,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE7EP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBH,OAAA;cAAAG,QAAA,gBACEH,OAAA;gBAAIE,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,gBACxEH,OAAA,CAACiC,GAAG;kBAAC/B,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,8CAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELP,OAAA;gBAAKE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCH,OAAA;kBAAKE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBH,OAAA;oBAAKE,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,gBAC/DH,OAAA;sBAAKE,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrCH,OAAA;wBAAKE,SAAS,EAAC,qGAAqG;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC5HP,OAAA;wBAAIE,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNP,OAAA;sBAAGE,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAA0B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eAENP,OAAA;oBAAKE,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,gBACnEH,OAAA;sBAAKE,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrCH,OAAA;wBAAKE,SAAS,EAAC,uGAAuG;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9HP,OAAA;wBAAIE,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNP,OAAA;sBAAGE,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENP,OAAA;kBAAKE,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBH,OAAA;oBAAKE,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,gBACjEH,OAAA;sBAAKE,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrCH,OAAA;wBAAKE,SAAS,EAAC,sGAAsG;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC7HP,OAAA;wBAAIE,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNP,OAAA;sBAAGE,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eAENP,OAAA;oBAAKE,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,gBACnEH,OAAA;sBAAKE,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrCH,OAAA;wBAAKE,SAAS,EAAC,uGAAuG;wBAAAC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC9HP,OAAA;wBAAIE,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACNP,OAAA;sBAAGE,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNP,OAAA;cAAKE,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5CH,OAAA;gBAAIE,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,gBACxEH,OAAA,CAACkC,MAAM;kBAAChC,SAAS,EAAC;gBAA6B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELP,OAAA;gBAAKE,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCH,OAAA;kBAAKE,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BH,OAAA;oBAAKE,SAAS,EAAC,gHAAgH;oBAAAC,QAAA,eAC7HH,OAAA,CAACmC,OAAO;sBAACjC,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACNP,OAAA;oBAAIE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DP,OAAA;oBAAGE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eAENP,OAAA;kBAAKE,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BH,OAAA;oBAAKE,SAAS,EAAC,kHAAkH;oBAAAC,QAAA,eAC/HH,OAAA,CAACoC,SAAS;sBAAClC,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACNP,OAAA;oBAAIE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DP,OAAA;oBAAGE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,eAENP,OAAA;kBAAKE,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BH,OAAA;oBAAKE,SAAS,EAAC,oHAAoH;oBAAAC,QAAA,eACjIH,OAAA,CAACqC,WAAW;sBAACnC,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNP,OAAA;oBAAIE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5DP,OAAA;oBAAGE,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC+B,EAAA,GAlRIrC,oBAAoB;AAoR1B,eAAeA,oBAAoB;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}