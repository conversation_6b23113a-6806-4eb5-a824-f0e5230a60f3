{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowDemo = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量，默认为0不显示任何步骤\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [hasStarted, setHasStarted] = useState(false); // 是否已经开始执行过\n\n  // 工作流步骤定义\n  const workflowSteps = [{\n    id: 'login',\n    name: '登录',\n    runningDescription: '正在验证登录信息...',\n    completedDescription: '已完成登录【Temu账号】名下的店铺1',\n    duration: 2000,\n    status: 'pending'\n  }, {\n    id: 'product_filter',\n    name: '商品筛选',\n    runningDescription: '正在筛选符合条件的商品...',\n    completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n    duration: 3000,\n    status: 'pending'\n  }, {\n    id: 'product_processing',\n    name: '商品加权',\n    runningDescription: '正在为商品设置流量加权...',\n    completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',\n    duration: 4000,\n    status: 'pending'\n  }, {\n    id: 'result',\n    name: '结果',\n    runningDescription: '正在生成执行结果...',\n    completedDescription: '执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',\n    duration: 1000,\n    status: 'pending'\n  }];\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = step => {\n    if (step.status === 'running') {\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n    setHasStarted(true);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态并显示第一步\n    if (failedStep === null) {\n      setVisibleSteps(1);\n      setSteps(prev => prev.map(step => ({\n        ...step,\n        status: 'pending'\n      })));\n    }\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        // 确保新显示的步骤处于pending状态\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'pending'\n        } : step));\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'running'\n      } : step));\n\n      // 等待步骤完成\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'failed',\n          errorMessage: '网络连接超时，请检查网络状态后重试'\n        } : step));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'completed'\n      } : step));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        // 确保下一步显示为pending状态\n        setSteps(prev => prev.map((step, index) => index === i + 1 ? {\n          ...step,\n          status: 'pending'\n        } : step));\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => index === failedStep ? {\n        ...step,\n        status: 'pending'\n      } : step));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(0); // 重置为0，不显示任何步骤\n    setFailedStep(null);\n    setHasStarted(false);\n    setSteps(workflowSteps.map(step => ({\n      ...step,\n      status: 'pending'\n    })));\n  };\n\n  // 保存计划功能\n  const savePlan = () => {\n    // 模拟保存计划的操作\n    const toast = document.createElement('div');\n    toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300';\n    toast.textContent = '✅ 计划已保存成功！';\n    document.body.appendChild(toast);\n\n    // 3秒后移除提示\n    setTimeout(() => {\n      toast.style.opacity = '0';\n      setTimeout(() => {\n        document.body.removeChild(toast);\n      }, 300);\n    }, 3000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-blue-500 text-white p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold\",\n              children: \"AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold\",\n              children: \"Temu\\u81EA\\u52A8\\u5316\\u52A9\\u624B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-100 text-sm\",\n              children: \"\\u6D41\\u91CF\\u52A0\\u901F\\u81EA\\u52A8\\u5316\\u6267\\u884C\\u4E2D...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 rounded-lg p-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-800 mb-2\",\n              children: \"\\u597D\\u7684\\uFF0C\\u5F00\\u59CB\\u6267\\u884C\\u8BA1\\u5212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600 bg-white rounded-lg p-3 border\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"\\u6267\\u884C\\u914D\\u7F6E\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), \"\\u9AD8\\u7EA7\\u6D41\\u91CF\\u52A0\\u6743\\u6863\\u4F4D\\uFF0C\\u4EF7\\u683C\\u8303\\u56F44-6\\u7F8E\\u5143\\uFF0C\\u65F6\\u654830\\u5929\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: steps.slice(0, visibleSteps).map((step, index) => {\n            const nextStep = steps[index + 1];\n            const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n            const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [shouldShowConnector && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`,\n                style: {\n                  height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4 relative z-10 pb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${step.status === 'completed' ? 'bg-green-500 text-white' : step.status === 'failed' ? 'bg-red-500 text-white' : step.status === 'running' ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'}`,\n                    children: [step.status === 'completed' && /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 57\n                    }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 54\n                    }, this), step.status === 'running' && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-5 h-5 animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 55\n                    }, this), step.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-3 h-3 rounded-full bg-gray-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 55\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0 pt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-lg font-medium text-gray-900\",\n                      children: step.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 23\n                    }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: retryFromFailed,\n                      disabled: isRunning,\n                      className: \"mt-2 text-xs bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1.5 rounded-full font-medium transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-1\",\n                      children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                        className: \"w-3 h-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u91CD\\u8BD5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 254,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 leading-relaxed\",\n                    children: getStepDescription(step)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 21\n                  }, this), step.status === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-2 bg-red-50 border border-red-200 rounded-lg p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-red-700 text-sm\",\n                      children: \"\\u6267\\u884C\\u5931\\u8D25\\uFF0C\\u53EF\\u80FD\\u662F\\u7F51\\u7EDC\\u8FDE\\u63A5\\u95EE\\u9898\\u6216\\u7CFB\\u7EDF\\u7E41\\u5FD9\\uFF0C\\u8BF7\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\\u3002\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [workflowStatus === 'idle' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: executeWorkflow,\n                disabled: isRunning,\n                className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Play, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5F00\\u59CB\\u6267\\u884C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 19\n              }, this), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: true,\n                className: \"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u4E2D...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: resetWorkflow,\n                className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), workflowStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: retryFromFailed,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u91CD\\u8BD5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\",\n                  children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Loader2, {\n                    className: \"w-4 h-4 text-white animate-spin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-blue-700 font-medium\",\n                  children: [\"\\u6B63\\u5728\\u6267\\u884C\\u7B2C \", currentStep + 1, \" \\u6B65\\uFF0C\\u5171 \", steps.length, \" \\u6B65\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetWorkflow,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\",\n              title: \"\\u91CD\\u65B0\\u5F00\\u59CB\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-green-700 font-semibold text-base mb-1\",\n                  children: \"\\uD83C\\uDF89 \\u81EA\\u52A8\\u5316\\u6267\\u884C\\u5B8C\\u6210\\uFF01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-green-600 text-sm\",\n                  children: \"\\u6240\\u6709\\u5546\\u54C1\\u5DF2\\u6210\\u529F\\u914D\\u7F6E\\u6D41\\u91CF\\u52A0\\u901F\\uFF0C\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), workflowStatus === 'failed' && failedStep !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-700 font-semibold text-base mb-1\",\n                  children: \"\\u6267\\u884C\\u4E2D\\u65AD\\uFF0C\\u9700\\u8981\\u5904\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-600 text-sm\",\n                  children: [\"\\u7B2C \", failedStep + 1, \" \\u6B65\\u51FA\\u73B0\\u95EE\\u9898\\uFF0C\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-white/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-semibold text-gray-900 mb-3 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), \"Demo\\u63A7\\u5236\\u9762\\u677F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 mb-4 leading-relaxed\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium\",\n          children: \"\\u6F14\\u793A\\u7279\\u6027\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-flex items-center ml-2 space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 49\n            }, this), \"\\u52A8\\u6001\\u63CF\\u8FF0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 49\n            }, this), \"\\u65F6\\u95F4\\u7EBF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 49\n            }, this), \"\\u5931\\u8D25\\u91CD\\u8BD5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 49\n            }, this), \"\\u5B8C\\u7F8E\\u8FDE\\u63A5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: executeWorkflow,\n          disabled: isRunning,\n          className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u6A21\\u62DF\\u6267\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetWorkflow,\n          className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u91CD\\u7F6E\\u72B6\\u6001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            // 快速演示失败场景\n            setVisibleSteps(3);\n            setSteps(prev => prev.map((step, index) => {\n              if (index === 0) return {\n                ...step,\n                status: 'completed'\n              };\n              if (index === 1) return {\n                ...step,\n                status: 'completed'\n              };\n              if (index === 2) return {\n                ...step,\n                status: 'failed'\n              };\n              return step;\n            }));\n            setWorkflowStatus('failed');\n            setFailedStep(2);\n          },\n          className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u6F14\\u793A\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n};\n_s(TemuWorkflowDemo, \"FWVJnaQOAAwGuXf7zigq+aTr170=\");\n_c = TemuWorkflowDemo;\nexport default TemuWorkflowDemo;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowDemo\");", "map": {"version": 3, "names": ["React", "useState", "Play", "RefreshCw", "CheckCircle", "XCircle", "AlertCircle", "Loader2", "Save", "jsxDEV", "_jsxDEV", "TemuWorkflowDemo", "_s", "currentStep", "setCurrentStep", "isRunning", "setIsRunning", "workflowStatus", "setWorkflowStatus", "visibleSteps", "setVisibleSteps", "failedStep", "setFailedStep", "hasStarted", "setHasStarted", "workflowSteps", "id", "name", "runningDescription", "completedDescription", "duration", "status", "steps", "setSteps", "getStepDescription", "step", "executeWorkflow", "startStep", "prev", "map", "i", "length", "index", "Promise", "resolve", "setTimeout", "shouldFail", "Math", "random", "errorMessage", "retryFromFailed", "resetWorkflow", "savePlan", "toast", "document", "createElement", "className", "textContent", "body", "append<PERSON><PERSON><PERSON>", "style", "opacity", "<PERSON><PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "slice", "nextStep", "shouldShowConnector", "connectorColor", "height", "onClick", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Save } from 'lucide-react';\n\nconst TemuWorkflowDemo = () => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(0); // 控制显示的步骤数量，默认为0不显示任何步骤\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [hasStarted, setHasStarted] = useState(false); // 是否已经开始执行过\n\n  // 工作流步骤定义\n  const workflowSteps = [\n    {\n      id: 'login',\n      name: '登录',\n      runningDescription: '正在验证登录信息...',\n      completedDescription: '已完成登录【Temu账号】名下的店铺1',\n      duration: 2000,\n      status: 'pending'\n    },\n    {\n      id: 'product_filter',\n      name: '商品筛选', \n      runningDescription: '正在筛选符合条件的商品...',\n      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n      duration: 3000,\n      status: 'pending'\n    },\n    {\n      id: 'product_processing',\n      name: '商品加权',\n      runningDescription: '正在为商品设置流量加权...',\n      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',\n      duration: 4000,\n      status: 'pending'\n    },\n    {\n      id: 'result',\n      name: '结果',\n      runningDescription: '正在生成执行结果...',\n      completedDescription: '执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',\n      duration: 1000,\n      status: 'pending'\n    }\n  ];\n\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = (step) => {\n    if (step.status === 'running') {\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n    setHasStarted(true);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态并显示第一步\n    if (failedStep === null) {\n      setVisibleSteps(1);\n      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));\n    }\n\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        // 确保新显示的步骤处于pending状态\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'pending' } : step\n        ));\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'running' } : step\n      ));\n\n      // 等待步骤完成\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'failed', errorMessage: '网络连接超时，请检查网络状态后重试' } : step\n        ));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'completed' } : step\n      ));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        // 确保下一步显示为pending状态\n        setSteps(prev => prev.map((step, index) =>\n          index === i + 1 ? { ...step, status: 'pending' } : step\n        ));\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => \n        index === failedStep ? { ...step, status: 'pending' } : step\n      ));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(0); // 重置为0，不显示任何步骤\n    setFailedStep(null);\n    setHasStarted(false);\n    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));\n  };\n\n  // 保存计划功能\n  const savePlan = () => {\n    // 模拟保存计划的操作\n    const toast = document.createElement('div');\n    toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300';\n    toast.textContent = '✅ 计划已保存成功！';\n    document.body.appendChild(toast);\n\n    // 3秒后移除提示\n    setTimeout(() => {\n      toast.style.opacity = '0';\n      setTimeout(() => {\n        document.body.removeChild(toast);\n      }, 300);\n    }, 3000);\n  };\n\n  return (\n    <div className=\"w-full\">\n      {/* AI对话框容器 */}\n      <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\">\n        {/* 对话框头部 */}\n        <div className=\"bg-blue-500 text-white p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-sm font-bold\">AI</span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold\">Temu自动化助手</h3>\n              <p className=\"text-blue-100 text-sm\">流量加速自动化执行中...</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 对话内容 */}\n        <div className=\"p-6\">\n          {/* AI消息 */}\n          <div className=\"mb-6\">\n            <div className=\"bg-gray-100 rounded-lg p-4 mb-4\">\n              <p className=\"text-gray-800 mb-2\">好的，开始执行计划</p>\n              <div className=\"text-sm text-gray-600 bg-white rounded-lg p-3 border\">\n                <span className=\"font-medium\">执行配置：</span>高级流量加权档位，价格范围4-6美元，时效30天\n              </div>\n            </div>\n          </div>\n\n          {/* 优化后的时间线步骤列表 - 完美连接线 */}\n          <div className=\"relative\">\n            {steps.slice(0, visibleSteps).map((step, index) => {\n              const nextStep = steps[index + 1];\n              const shouldShowConnector = index < visibleSteps - 1 && index < steps.length - 1;\n              const connectorColor = step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300';\n\n              return (\n                <div key={step.id} className=\"relative\">\n                  {/* 连接线 - 只在步骤完成后显示绿色，否则显示灰色 */}\n                  {shouldShowConnector && (\n                    <div\n                      className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${connectorColor}`}\n                      style={{\n                        height: '3rem' // 固定高度，只连接到下一个步骤的起始位置\n                      }}\n                    ></div>\n                  )}\n\n                  {/* 步骤内容容器 */}\n                  <div className=\"flex items-start space-x-4 relative z-10 pb-6\">\n                    {/* 状态图标容器 - 优化层级确保完美覆盖 */}\n                    <div className=\"flex-shrink-0 relative\">\n                      {/* 图标背景圆圈 - 确保完全覆盖连接线 */}\n                      <div className=\"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"></div>\n\n                      {/* 状态图标 - 最高层级 */}\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${\n                        step.status === 'completed' ? 'bg-green-500 text-white' :\n                        step.status === 'failed' ? 'bg-red-500 text-white' :\n                        step.status === 'running' ? 'bg-blue-500 text-white' :\n                        'bg-gray-300 text-gray-600'\n                      }`}>\n                        {step.status === 'completed' && <CheckCircle className=\"w-5 h-5\" />}\n                        {step.status === 'failed' && <XCircle className=\"w-5 h-5\" />}\n                        {step.status === 'running' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                        {step.status === 'pending' && <div className=\"w-3 h-3 rounded-full bg-gray-600\"></div>}\n                      </div>\n                    </div>\n\n                  {/* 步骤内容 */}\n                  <div className=\"flex-1 min-w-0 pt-1\">\n                    <div className=\"mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">{step.name}</h4>\n                      {/* 失败状态的重试按钮 */}\n                      {step.status === 'failed' && (\n                        <button\n                          onClick={retryFromFailed}\n                          disabled={isRunning}\n                          className=\"mt-2 text-xs bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1.5 rounded-full font-medium transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-1\"\n                        >\n                          <RefreshCw className=\"w-3 h-3\" />\n                          <span>重试</span>\n                        </button>\n                      )}\n                    </div>\n\n                    <p className=\"text-sm text-gray-600 leading-relaxed\">\n                      {getStepDescription(step)}\n                    </p>\n\n                    {/* 失败时的错误信息 */}\n                    {step.status === 'failed' && (\n                      <div className=\"mt-2 bg-red-50 border border-red-200 rounded-lg p-3\">\n                        <p className=\"text-red-700 text-sm\">\n                          执行失败，可能是网络连接问题或系统繁忙，请点击重试继续执行。\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            );\n            })}\n          </div>\n\n          {/* 操作按钮区域 */}\n          <div className=\"mt-8 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                {workflowStatus === 'idle' && (\n                  <button\n                    onClick={executeWorkflow}\n                    disabled={isRunning}\n                    className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                  >\n                    <Play className=\"w-4 h-4\" />\n                    <span>开始执行</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'running' && (\n                  <button\n                    disabled\n                    className=\"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span>执行中...</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'completed' && (\n                  <button\n                    onClick={resetWorkflow}\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                  >\n                    <CheckCircle className=\"w-4 h-4\" />\n                    <span>执行完成</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'failed' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={retryFromFailed}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <RefreshCw className=\"w-4 h-4\" />\n                      <span>重试</span>\n                    </button>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\"\n                    >\n                      重新开始\n                    </button>\n                  </div>\n                )}\n\n                {/* 整体进度指示 */}\n                {workflowStatus === 'running' && (\n                  <div className=\"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\">\n                      <Loader2 className=\"w-4 h-4 text-white animate-spin\" />\n                    </div>\n                    <span className=\"text-sm text-blue-700 font-medium\">\n                      正在执行第 {currentStep + 1} 步，共 {steps.length} 步\n                    </span>\n                  </div>\n                )}\n              </div>\n\n              {/* 刷新按钮 */}\n              <button\n                onClick={resetWorkflow}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\"\n                title=\"重新开始\"\n              >\n                <RefreshCw className=\"w-4 h-4\" />\n              </button>\n            </div>\n\n            {/* 状态指示器 */}\n            {workflowStatus === 'completed' && (\n              <div className=\"mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <CheckCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-green-700 font-semibold text-base mb-1\">\n                      🎉 自动化执行完成！\n                    </div>\n                    <div className=\"text-green-600 text-sm\">\n                      所有商品已成功配置流量加速，系统运行正常\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {workflowStatus === 'failed' && failedStep !== null && (\n              <div className=\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-red-700 font-semibold text-base mb-1\">\n                      执行中断，需要处理\n                    </div>\n                    <div className=\"text-red-600 text-sm\">\n                      第 {failedStep + 1} 步出现问题，可点击重试继续执行\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Demo控制面板 */}\n      <div className=\"mt-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-white/20\">\n        <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center\">\n          <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></div>\n          Demo控制面板\n        </h4>\n        <p className=\"text-sm text-gray-600 mb-4 leading-relaxed\">\n          <span className=\"font-medium\">演示特性：</span>\n          <span className=\"inline-flex items-center ml-2 space-x-3\">\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />动态描述</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />时间线</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />失败重试</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />完美连接</span>\n          </span>\n        </p>\n        <div className=\"flex flex-wrap gap-3\">\n          <button\n            onClick={executeWorkflow}\n            disabled={isRunning}\n            className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>🚀</span>\n            <span>模拟执行</span>\n          </button>\n          <button\n            onClick={resetWorkflow}\n            className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>🔄</span>\n            <span>重置状态</span>\n          </button>\n          <button\n            onClick={() => {\n              // 快速演示失败场景\n              setVisibleSteps(3);\n              setSteps(prev => prev.map((step, index) => {\n                if (index === 0) return { ...step, status: 'completed' };\n                if (index === 1) return { ...step, status: 'completed' };\n                if (index === 2) return { ...step, status: 'failed' };\n                return step;\n              }));\n              setWorkflowStatus('failed');\n              setFailedStep(2);\n            }}\n            className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>⚠️</span>\n            <span>演示失败</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TemuWorkflowDemo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAErD;EACA,MAAMwB,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,aAAa;IACjCC,oBAAoB,EAAE,qBAAqB;IAC3CC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,2CAA2C;IACjEC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,oBAAoB;IACxBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,2BAA2B;IACjDC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,IAAI;IACVC,kBAAkB,EAAE,aAAa;IACjCC,oBAAoB,EAAE,0CAA0C;IAChEC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAACwB,aAAa,CAAC;;EAEjD;EACA,MAAMS,kBAAkB,GAAIC,IAAI,IAAK;IACnC,IAAIA,IAAI,CAACJ,MAAM,KAAK,SAAS,EAAE;MAC7B,OAAOI,IAAI,CAACP,kBAAkB;IAChC,CAAC,MAAM,IAAIO,IAAI,CAACJ,MAAM,KAAK,WAAW,EAAE;MACtC,OAAOI,IAAI,CAACN,oBAAoB;IAClC,CAAC,MAAM,IAAIM,IAAI,CAACJ,MAAM,KAAK,QAAQ,EAAE;MACnC,OAAO,QAAQI,IAAI,CAACP,kBAAkB,EAAE;IAC1C;IACA,OAAO,SAAS;EAClB,CAAC;;EAED;EACA,MAAMQ,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCpB,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,SAAS,CAAC;IAC5BI,aAAa,CAAC,IAAI,CAAC;IACnBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMa,SAAS,GAAGhB,UAAU,KAAK,IAAI,GAAGA,UAAU,GAAG,CAAC;IACtDP,cAAc,CAACuB,SAAS,CAAC;;IAEzB;IACA,IAAIhB,UAAU,KAAK,IAAI,EAAE;MACvBD,eAAe,CAAC,CAAC,CAAC;MAClBa,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACJ,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,CAAC,CAAC,CAAC;IACtE;IAEA,KAAK,IAAIS,CAAC,GAAGH,SAAS,EAAEG,CAAC,GAAGR,KAAK,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C1B,cAAc,CAAC0B,CAAC,CAAC;;MAEjB;MACA,IAAIA,CAAC,GAAG,CAAC,GAAGrB,YAAY,EAAE;QACxBC,eAAe,CAACoB,CAAC,GAAG,CAAC,CAAC;QACtB;QACAP,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEJ,MAAM,EAAE;QAAU,CAAC,GAAGI,IACjD,CAAC,CAAC;QACF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1D;;MAEA;MACAX,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;QAAE,GAAGL,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,GAAGI,IACjD,CAAC,CAAC;;MAEF;MACA,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEZ,KAAK,CAACQ,CAAC,CAAC,CAACV,QAAQ,CAAC,CAAC;;MAEpE;MACA,MAAMgB,UAAU,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,KAAKR,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC;MAE/D,IAAIM,UAAU,EAAE;QACdb,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEJ,MAAM,EAAE,QAAQ;UAAEkB,YAAY,EAAE;QAAoB,CAAC,GAAGd,IACnF,CAAC,CAAC;QACFjB,iBAAiB,CAAC,QAAQ,CAAC;QAC3BI,aAAa,CAACkB,CAAC,CAAC;QAChBxB,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;;MAEA;MACAiB,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG;QAAE,GAAGL,IAAI;QAAEJ,MAAM,EAAE;MAAY,CAAC,GAAGI,IACnD,CAAC,CAAC;;MAEF;MACA,IAAIK,CAAC,GAAGR,KAAK,CAACS,MAAM,GAAG,CAAC,EAAE;QACxBrB,eAAe,CAACoB,CAAC,GAAG,CAAC,CAAC;QACtB;QACAP,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKF,CAAC,GAAG,CAAC,GAAG;UAAE,GAAGL,IAAI;UAAEJ,MAAM,EAAE;QAAU,CAAC,GAAGI,IACrD,CAAC,CAAC;QACF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1D;IACF;IAEA1B,iBAAiB,CAAC,WAAW,CAAC;IAC9BI,aAAa,CAAC,IAAI,CAAC;IACnBN,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI7B,UAAU,KAAK,IAAI,EAAE;MACvB;MACAY,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KACpCA,KAAK,KAAKrB,UAAU,GAAG;QAAE,GAAGc,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,GAAGI,IAC1D,CAAC,CAAC;MACFC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,aAAa,GAAGA,CAAA,KAAM;IAC1BnC,YAAY,CAAC,KAAK,CAAC;IACnBE,iBAAiB,CAAC,MAAM,CAAC;IACzBJ,cAAc,CAAC,CAAC,CAAC;IACjBM,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACpBE,aAAa,CAAC,IAAI,CAAC;IACnBE,aAAa,CAAC,KAAK,CAAC;IACpBS,QAAQ,CAACR,aAAa,CAACc,GAAG,CAACJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEJ,MAAM,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;EACvE,CAAC;;EAED;EACA,MAAMqB,QAAQ,GAAGA,CAAA,KAAM;IACrB;IACA,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC3CF,KAAK,CAACG,SAAS,GAAG,6GAA6G;IAC/HH,KAAK,CAACI,WAAW,GAAG,YAAY;IAChCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,KAAK,CAAC;;IAEhC;IACAR,UAAU,CAAC,MAAM;MACfQ,KAAK,CAACO,KAAK,CAACC,OAAO,GAAG,GAAG;MACzBhB,UAAU,CAAC,MAAM;QACfS,QAAQ,CAACI,IAAI,CAACI,WAAW,CAACT,KAAK,CAAC;MAClC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACE3C,OAAA;IAAK8C,SAAS,EAAC,QAAQ;IAAAO,QAAA,gBAErBrD,OAAA;MAAK8C,SAAS,EAAC,2FAA2F;MAAAO,QAAA,gBAExGrD,OAAA;QAAK8C,SAAS,EAAC,4BAA4B;QAAAO,QAAA,eACzCrD,OAAA;UAAK8C,SAAS,EAAC,6BAA6B;UAAAO,QAAA,gBAC1CrD,OAAA;YAAK8C,SAAS,EAAC,mEAAmE;YAAAO,QAAA,eAChFrD,OAAA;cAAM8C,SAAS,EAAC,mBAAmB;cAAAO,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNzD,OAAA;YAAAqD,QAAA,gBACErD,OAAA;cAAI8C,SAAS,EAAC,eAAe;cAAAO,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5CzD,OAAA;cAAG8C,SAAS,EAAC,uBAAuB;cAAAO,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzD,OAAA;QAAK8C,SAAS,EAAC,KAAK;QAAAO,QAAA,gBAElBrD,OAAA;UAAK8C,SAAS,EAAC,MAAM;UAAAO,QAAA,eACnBrD,OAAA;YAAK8C,SAAS,EAAC,iCAAiC;YAAAO,QAAA,gBAC9CrD,OAAA;cAAG8C,SAAS,EAAC,oBAAoB;cAAAO,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/CzD,OAAA;cAAK8C,SAAS,EAAC,sDAAsD;cAAAO,QAAA,gBACnErD,OAAA;gBAAM8C,SAAS,EAAC,aAAa;gBAAAO,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,2HAC5C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzD,OAAA;UAAK8C,SAAS,EAAC,UAAU;UAAAO,QAAA,EACtB/B,KAAK,CAACoC,KAAK,CAAC,CAAC,EAAEjD,YAAY,CAAC,CAACoB,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KAAK;YACjD,MAAM2B,QAAQ,GAAGrC,KAAK,CAACU,KAAK,GAAG,CAAC,CAAC;YACjC,MAAM4B,mBAAmB,GAAG5B,KAAK,GAAGvB,YAAY,GAAG,CAAC,IAAIuB,KAAK,GAAGV,KAAK,CAACS,MAAM,GAAG,CAAC;YAChF,MAAM8B,cAAc,GAAGpC,IAAI,CAACJ,MAAM,KAAK,WAAW,GAAG,cAAc,GAAG,aAAa;YAEnF,oBACErB,OAAA;cAAmB8C,SAAS,EAAC,UAAU;cAAAO,QAAA,GAEpCO,mBAAmB,iBAClB5D,OAAA;gBACE8C,SAAS,EAAE,+DAA+De,cAAc,EAAG;gBAC3FX,KAAK,EAAE;kBACLY,MAAM,EAAE,MAAM,CAAC;gBACjB;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP,eAGDzD,OAAA;gBAAK8C,SAAS,EAAC,+CAA+C;gBAAAO,QAAA,gBAE5DrD,OAAA;kBAAK8C,SAAS,EAAC,wBAAwB;kBAAAO,QAAA,gBAErCrD,OAAA;oBAAK8C,SAAS,EAAC;kBAA4E;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGlGzD,OAAA;oBAAK8C,SAAS,EAAE,6GACdrB,IAAI,CAACJ,MAAM,KAAK,WAAW,GAAG,yBAAyB,GACvDI,IAAI,CAACJ,MAAM,KAAK,QAAQ,GAAG,uBAAuB,GAClDI,IAAI,CAACJ,MAAM,KAAK,SAAS,GAAG,wBAAwB,GACpD,2BAA2B,EAC1B;oBAAAgC,QAAA,GACA5B,IAAI,CAACJ,MAAM,KAAK,WAAW,iBAAIrB,OAAA,CAACN,WAAW;sBAACoD,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAClEhC,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBAAIrB,OAAA,CAACL,OAAO;sBAACmD,SAAS,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC3DhC,IAAI,CAACJ,MAAM,KAAK,SAAS,iBAAIrB,OAAA,CAACH,OAAO;sBAACiD,SAAS,EAAC;oBAAsB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACzEhC,IAAI,CAACJ,MAAM,KAAK,SAAS,iBAAIrB,OAAA;sBAAK8C,SAAS,EAAC;oBAAkC;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGRzD,OAAA;kBAAK8C,SAAS,EAAC,qBAAqB;kBAAAO,QAAA,gBAClCrD,OAAA;oBAAK8C,SAAS,EAAC,MAAM;oBAAAO,QAAA,gBACnBrD,OAAA;sBAAI8C,SAAS,EAAC,mCAAmC;sBAAAO,QAAA,EAAE5B,IAAI,CAACR;oBAAI;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAEjEhC,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBACvBrB,OAAA;sBACE+D,OAAO,EAAEvB,eAAgB;sBACzBwB,QAAQ,EAAE3D,SAAU;sBACpByC,SAAS,EAAC,2TAA2T;sBAAAO,QAAA,gBAErUrD,OAAA,CAACP,SAAS;wBAACqD,SAAS,EAAC;sBAAS;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjCzD,OAAA;wBAAAqD,QAAA,EAAM;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAENzD,OAAA;oBAAG8C,SAAS,EAAC,uCAAuC;oBAAAO,QAAA,EACjD7B,kBAAkB,CAACC,IAAI;kBAAC;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,EAGHhC,IAAI,CAACJ,MAAM,KAAK,QAAQ,iBACvBrB,OAAA;oBAAK8C,SAAS,EAAC,qDAAqD;oBAAAO,QAAA,eAClErD,OAAA;sBAAG8C,SAAS,EAAC,sBAAsB;sBAAAO,QAAA,EAAC;oBAEpC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GA9DIhC,IAAI,CAACT,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+Dd,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNzD,OAAA;UAAK8C,SAAS,EAAC,oCAAoC;UAAAO,QAAA,gBACjDrD,OAAA;YAAK8C,SAAS,EAAC,mCAAmC;YAAAO,QAAA,gBAChDrD,OAAA;cAAK8C,SAAS,EAAC,6BAA6B;cAAAO,QAAA,GACzC9C,cAAc,KAAK,MAAM,iBACxBP,OAAA;gBACE+D,OAAO,EAAErC,eAAgB;gBACzBsC,QAAQ,EAAE3D,SAAU;gBACpByC,SAAS,EAAC,0SAA0S;gBAAAO,QAAA,gBAEpTrD,OAAA,CAACR,IAAI;kBAACsD,SAAS,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BzD,OAAA;kBAAAqD,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT,EAEAlD,cAAc,KAAK,SAAS,iBAC3BP,OAAA;gBACEgE,QAAQ;gBACRlB,SAAS,EAAC,+IAA+I;gBAAAO,QAAA,gBAEzJrD,OAAA,CAACH,OAAO;kBAACiD,SAAS,EAAC;gBAAsB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CzD,OAAA;kBAAAqD,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACT,EAEAlD,cAAc,KAAK,WAAW,iBAC7BP,OAAA;gBACE+D,OAAO,EAAEtB,aAAc;gBACvBK,SAAS,EAAC,8PAA8P;gBAAAO,QAAA,gBAExQrD,OAAA,CAACN,WAAW;kBAACoD,SAAS,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnCzD,OAAA;kBAAAqD,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT,EAEAlD,cAAc,KAAK,QAAQ,iBAC1BP,OAAA;gBAAK8C,SAAS,EAAC,6BAA6B;gBAAAO,QAAA,gBAC1CrD,OAAA;kBACE+D,OAAO,EAAEvB,eAAgB;kBACzBwB,QAAQ,EAAE3D,SAAU;kBACpByC,SAAS,EAAC,kTAAkT;kBAAAO,QAAA,gBAE5TrD,OAAA,CAACP,SAAS;oBAACqD,SAAS,EAAC;kBAAS;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjCzD,OAAA;oBAAAqD,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACTzD,OAAA;kBACE+D,OAAO,EAAEtB,aAAc;kBACvBK,SAAS,EAAC,8NAA8N;kBAAAO,QAAA,EACzO;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAGAlD,cAAc,KAAK,SAAS,iBAC3BP,OAAA;gBAAK8C,SAAS,EAAC,+HAA+H;gBAAAO,QAAA,gBAC5IrD,OAAA;kBAAK8C,SAAS,EAAC,kGAAkG;kBAAAO,QAAA,eAC/GrD,OAAA,CAACH,OAAO;oBAACiD,SAAS,EAAC;kBAAiC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACNzD,OAAA;kBAAM8C,SAAS,EAAC,mCAAmC;kBAAAO,QAAA,GAAC,iCAC5C,EAAClD,WAAW,GAAG,CAAC,EAAC,sBAAK,EAACmB,KAAK,CAACS,MAAM,EAAC,SAC5C;gBAAA;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNzD,OAAA;cACE+D,OAAO,EAAEtB,aAAc;cACvBK,SAAS,EAAC,gHAAgH;cAC1HmB,KAAK,EAAC,0BAAM;cAAAZ,QAAA,eAEZrD,OAAA,CAACP,SAAS;gBAACqD,SAAS,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLlD,cAAc,KAAK,WAAW,iBAC7BP,OAAA;YAAK8C,SAAS,EAAC,uGAAuG;YAAAO,QAAA,eACpHrD,OAAA;cAAK8C,SAAS,EAAC,6BAA6B;cAAAO,QAAA,gBAC1CrD,OAAA;gBAAK8C,SAAS,EAAC,kHAAkH;gBAAAO,QAAA,eAC/HrD,OAAA,CAACN,WAAW;kBAACoD,SAAS,EAAC;gBAAoB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNzD,OAAA;gBAAAqD,QAAA,gBACErD,OAAA;kBAAK8C,SAAS,EAAC,6CAA6C;kBAAAO,QAAA,EAAC;gBAE7D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNzD,OAAA;kBAAK8C,SAAS,EAAC,wBAAwB;kBAAAO,QAAA,EAAC;gBAExC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAlD,cAAc,KAAK,QAAQ,IAAII,UAAU,KAAK,IAAI,iBACjDX,OAAA;YAAK8C,SAAS,EAAC,kGAAkG;YAAAO,QAAA,eAC/GrD,OAAA;cAAK8C,SAAS,EAAC,6BAA6B;cAAAO,QAAA,gBAC1CrD,OAAA;gBAAK8C,SAAS,EAAC,+GAA+G;gBAAAO,QAAA,eAC5HrD,OAAA,CAACJ,WAAW;kBAACkD,SAAS,EAAC;gBAAoB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNzD,OAAA;gBAAAqD,QAAA,gBACErD,OAAA;kBAAK8C,SAAS,EAAC,2CAA2C;kBAAAO,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNzD,OAAA;kBAAK8C,SAAS,EAAC,sBAAsB;kBAAAO,QAAA,GAAC,SAClC,EAAC1C,UAAU,GAAG,CAAC,EAAC,6FACpB;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzD,OAAA;MAAK8C,SAAS,EAAC,iGAAiG;MAAAO,QAAA,gBAC9GrD,OAAA;QAAI8C,SAAS,EAAC,oDAAoD;QAAAO,QAAA,gBAChErD,OAAA;UAAK8C,SAAS,EAAC;QAAuC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gCAE/D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzD,OAAA;QAAG8C,SAAS,EAAC,4CAA4C;QAAAO,QAAA,gBACvDrD,OAAA;UAAM8C,SAAS,EAAC,aAAa;UAAAO,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CzD,OAAA;UAAM8C,SAAS,EAAC,yCAAyC;UAAAO,QAAA,gBACvDrD,OAAA;YAAM8C,SAAS,EAAC,mBAAmB;YAAAO,QAAA,gBAACrD,OAAA,CAACN,WAAW;cAACoD,SAAS,EAAC;YAA6B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtGzD,OAAA;YAAM8C,SAAS,EAAC,mBAAmB;YAAAO,QAAA,gBAACrD,OAAA,CAACN,WAAW;cAACoD,SAAS,EAAC;YAA6B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAAG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrGzD,OAAA;YAAM8C,SAAS,EAAC,mBAAmB;YAAAO,QAAA,gBAACrD,OAAA,CAACN,WAAW;cAACoD,SAAS,EAAC;YAA6B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtGzD,OAAA;YAAM8C,SAAS,EAAC,mBAAmB;YAAAO,QAAA,gBAACrD,OAAA,CAACN,WAAW;cAACoD,SAAS,EAAC;YAA6B;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACJzD,OAAA;QAAK8C,SAAS,EAAC,sBAAsB;QAAAO,QAAA,gBACnCrD,OAAA;UACE+D,OAAO,EAAErC,eAAgB;UACzBsC,QAAQ,EAAE3D,SAAU;UACpByC,SAAS,EAAC,qRAAqR;UAAAO,QAAA,gBAE/RrD,OAAA;YAAAqD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfzD,OAAA;YAAAqD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACTzD,OAAA;UACE+D,OAAO,EAAEtB,aAAc;UACvBK,SAAS,EAAC,qOAAqO;UAAAO,QAAA,gBAE/OrD,OAAA;YAAAqD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfzD,OAAA;YAAAqD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACTzD,OAAA;UACE+D,OAAO,EAAEA,CAAA,KAAM;YACb;YACArD,eAAe,CAAC,CAAC,CAAC;YAClBa,QAAQ,CAACK,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACJ,IAAI,EAAEO,KAAK,KAAK;cACzC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGP,IAAI;gBAAEJ,MAAM,EAAE;cAAY,CAAC;cACxD,IAAIW,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGP,IAAI;gBAAEJ,MAAM,EAAE;cAAY,CAAC;cACxD,IAAIW,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGP,IAAI;gBAAEJ,MAAM,EAAE;cAAS,CAAC;cACrD,OAAOI,IAAI;YACb,CAAC,CAAC,CAAC;YACHjB,iBAAiB,CAAC,QAAQ,CAAC;YAC3BI,aAAa,CAAC,CAAC,CAAC;UAClB,CAAE;UACFkC,SAAS,EAAC,6OAA6O;UAAAO,QAAA,gBAEvPrD,OAAA;YAAAqD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfzD,OAAA;YAAAqD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CA5bID,gBAAgB;AAAAiE,EAAA,GAAhBjE,gBAAgB;AA8btB,eAAeA,gBAAgB;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}