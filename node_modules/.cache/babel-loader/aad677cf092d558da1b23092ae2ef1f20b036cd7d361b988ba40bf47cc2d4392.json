{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TestPage.jsx\";\nimport React from 'react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-cyan-50 p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900 mb-2\",\n          children: \"Temu\\u5DE5\\u4F5C\\u6D41\\u6F14\\u793A - \\u4FEE\\u590D\\u7248\\u672C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"\\u6D4B\\u8BD5\\u4FEE\\u590D\\u540E\\u7684\\u65F6\\u95F4\\u7EBF\\u7EC4\\u4EF6\\u529F\\u80FD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TemuWorkflowDemo, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 bg-white rounded-lg p-6 shadow-sm\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"\\u4FEE\\u590D\\u5185\\u5BB9\\u8BF4\\u660E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3 text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 font-bold\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5C45\\u4E2D\\u5BF9\\u9F50\\u4FEE\\u590D\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 21\n              }, this), \"\\u65F6\\u95F4\\u7EBF\\u7EC4\\u4EF6\\u5B8C\\u5168\\u5C45\\u4E2D\\u5BF9\\u9F50\\uFF0C\\u6B65\\u9AA4\\u56FE\\u6807\\u3001\\u8FDE\\u63A5\\u7EBF\\u548C\\u5185\\u5BB9\\u6587\\u672C\\u5782\\u76F4\\u5C45\\u4E2D\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 font-bold\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u8FDE\\u63A5\\u7EBF\\u6EA2\\u51FA\\u4FEE\\u590D\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 21\n              }, this), \"\\u7CBE\\u786E\\u63A7\\u5236\\u8FDE\\u63A5\\u7EBF\\u957F\\u5EA6\\uFF0C\\u56FA\\u5B9A\\u9AD8\\u5EA64rem\\uFF0C\\u4E0D\\u4F1A\\u5EF6\\u4F38\\u5230\\u4E0D\\u5E94\\u8BE5\\u51FA\\u73B0\\u7684\\u533A\\u57DF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 font-bold\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u6267\\u884C\\u6210\\u529F\\u63D0\\u793A\\u4F4D\\u7F6E\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 21\n              }, this), \"\\u8C03\\u6574\\u4E3A\\u4E0E\\u5931\\u8D25\\u63D0\\u793A\\u76F8\\u540C\\u7684\\u4F4D\\u7F6E\\u548C\\u5E03\\u5C40\\u6837\\u5F0F\\uFF0C\\u4FDD\\u6301UI\\u4E00\\u81F4\\u6027\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 font-bold\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u91CD\\u8BD5\\u6309\\u94AE\\u4F18\\u5316\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 21\n              }, this), \"\\u6539\\u4E3A\\u5706\\u5F62\\u56FE\\u6807\\u6309\\u94AE\\uFF0C\\u4F4D\\u4E8E\\u9519\\u8BEF\\u4FE1\\u606F\\u6846\\u4E0B\\u65B9\\uFF0C\\u5177\\u6709\\u65CB\\u8F6C\\u52A8\\u753B\\u6548\\u679C\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-green-500 font-bold\",\n              children: \"\\u2713\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u54CD\\u5E94\\u5F0F\\u8BBE\\u8BA1\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 21\n              }, this), \"\\u4FDD\\u6301\\u6E10\\u53D8\\u8272\\u5F69\\u3001\\u52A8\\u753B\\u6548\\u679C\\u548C\\u73B0\\u6709\\u4EA4\\u4E92\\u903B\\u8F91\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = TestPage;\nexport default TestPage;\nvar _c;\n$RefreshReg$(_c, \"TestPage\");", "map": {"version": 3, "names": ["React", "TemuWorkflowDemo", "jsxDEV", "_jsxDEV", "TestPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TestPage.jsx"], "sourcesContent": ["import React from 'react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\n\nconst TestPage = () => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-cyan-50 p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Temu工作流演示 - 修复版本\n          </h1>\n          <p className=\"text-gray-600\">\n            测试修复后的时间线组件功能\n          </p>\n        </div>\n        \n        <TemuWorkflowDemo />\n        \n        <div className=\"mt-8 bg-white rounded-lg p-6 shadow-sm\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">修复内容说明</h2>\n          <div className=\"space-y-3 text-sm text-gray-600\">\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-green-500 font-bold\">✓</span>\n              <span><strong>居中对齐修复：</strong>时间线组件完全居中对齐，步骤图标、连接线和内容文本垂直居中</span>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-green-500 font-bold\">✓</span>\n              <span><strong>连接线溢出修复：</strong>精确控制连接线长度，固定高度4rem，不会延伸到不应该出现的区域</span>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-green-500 font-bold\">✓</span>\n              <span><strong>执行成功提示位置：</strong>调整为与失败提示相同的位置和布局样式，保持UI一致性</span>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-green-500 font-bold\">✓</span>\n              <span><strong>重试按钮优化：</strong>改为圆形图标按钮，位于错误信息框下方，具有旋转动画效果</span>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-green-500 font-bold\">✓</span>\n              <span><strong>响应式设计：</strong>保持渐变色彩、动画效果和现有交互逻辑</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TestPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA;IAAKE,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eACnFH,OAAA;MAAKE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCH,OAAA;QAAKE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BH,OAAA;UAAIE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENP,OAAA,CAACF,gBAAgB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEpBP,OAAA;QAAKE,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDH,OAAA;UAAIE,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEP,OAAA;UAAKE,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CH,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA;cAAME,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDP,OAAA;cAAAG,QAAA,gBAAMH,OAAA;gBAAAG,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kLAA6B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA;cAAME,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDP,OAAA;cAAAG,QAAA,gBAAMH,OAAA;gBAAAG,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gLAAgC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA;cAAME,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDP,OAAA;cAAAG,QAAA,gBAAMH,OAAA;gBAAAG,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,sJAA0B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA;cAAME,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDP,OAAA;cAAAG,QAAA,gBAAMH,OAAA;gBAAAG,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,sKAA2B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCH,OAAA;cAAME,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDP,OAAA;cAAAG,QAAA,gBAAMH,OAAA;gBAAAG,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gHAAkB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GA3CIP,QAAQ;AA6Cd,eAAeA,QAAQ;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}