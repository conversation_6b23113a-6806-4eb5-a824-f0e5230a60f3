{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Clock } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowDemo = () => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(-1); // -1表示未开始\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [executionResults, setExecutionResults] = useState(null); // 执行结果详情\n\n  // 工作流步骤定义 - 增强版本，包含失败详情\n  const workflowSteps = [{\n    id: 'login',\n    name: '登录验证',\n    runningDescription: '正在验证Temu账号登录状态...',\n    completedDescription: '已成功登录【Temu账号】名下的店铺1',\n    failedDescription: '登录验证失败，请检查账号状态',\n    duration: 2000,\n    status: 'waiting'\n  }, {\n    id: 'product_filter',\n    name: '商品筛选',\n    runningDescription: '正在筛选符合条件的商品...',\n    completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n    failedDescription: '商品筛选失败，可能是网络连接问题',\n    duration: 3000,\n    status: 'waiting'\n  }, {\n    id: 'product_processing',\n    name: '商品加权处理',\n    runningDescription: '正在为商品设置流量加权配置...',\n    completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',\n    failedDescription: '商品加权处理失败，部分商品无法配置',\n    duration: 4000,\n    status: 'waiting'\n  }, {\n    id: 'result_summary',\n    name: '结果汇总',\n    runningDescription: '正在生成执行结果报告...',\n    completedDescription: '任务执行完成，正在生成详细报告',\n    failedDescription: '结果汇总失败，无法生成完整报告',\n    duration: 1500,\n    status: 'waiting'\n  }];\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = step => {\n    switch (step.status) {\n      case 'running':\n        return step.runningDescription;\n      case 'completed':\n        return step.completedDescription;\n      case 'failed':\n        return step.failedDescription;\n      case 'waiting':\n        return '等待执行...';\n      default:\n        return '等待执行...';\n    }\n  };\n\n  // 生成详细的执行结果报告\n  const generateExecutionReport = () => {\n    const successfulProducts = ['【智能手机壳-透明款】', '【蓝牙耳机-运动版】', '【充电宝-快充版】', '【数据线-Type-C】', '【手机支架-桌面版】', '【无线充电器】'];\n    const failedProducts = [{\n      name: '【高端手机壳-奢华版】',\n      reason: 'AI判断其价格为6.8美元，高于5美元的设定而被跳过'\n    }, {\n      name: '【专业摄影灯】',\n      reason: '页面长时间无法打开而执行失败'\n    }];\n    const report = {\n      totalProcessed: 200,\n      successful: 186,\n      failed: 14,\n      totalCost: 892.40,\n      successfulProducts: successfulProducts.slice(0, 6),\n      failedProducts: failedProducts,\n      executionTime: '8分32秒'\n    };\n    return report;\n  };\n\n  // 模拟工作流执行 - 重新设计的执行逻辑\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setExecutionResults(null);\n\n    // 从失败步骤开始，或从第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是全新开始，重置所有步骤状态\n    if (failedStep === null) {\n      setSteps(prev => prev.map(step => ({\n        ...step,\n        status: 'waiting'\n      })));\n    } else {\n      // 如果是重试，只重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => index === failedStep ? {\n        ...step,\n        status: 'waiting'\n      } : step));\n      setFailedStep(null);\n    }\n\n    // 执行工作流步骤\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'running'\n      } : step));\n\n      // 模拟步骤执行时间\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 模拟随机失败（20%概率，主要在第2和第3步）\n      const shouldFail = Math.random() < 0.2 && (i === 1 || i === 2);\n      if (shouldFail) {\n        // 步骤失败\n        setSteps(prev => prev.map((step, index) => index === i ? {\n          ...step,\n          status: 'failed'\n        } : step));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 步骤成功完成\n      setSteps(prev => prev.map((step, index) => index === i ? {\n        ...step,\n        status: 'completed'\n      } : step));\n\n      // 短暂延迟，让用户看到状态变化\n      await new Promise(resolve => setTimeout(resolve, 300));\n    }\n\n    // 所有步骤完成，生成执行结果\n    const report = generateExecutionReport();\n    setExecutionResults(report);\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n    setCurrentStep(-1);\n  };\n\n  // 重试失败的步骤 - 精准重试机制\n  const retryFromFailed = () => {\n    if (failedStep !== null && !isRunning) {\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(-1);\n    setFailedStep(null);\n    setExecutionResults(null);\n    setSteps(workflowSteps.map(step => ({\n      ...step,\n      status: 'waiting'\n    })));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full max-w-4xl mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/30\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `p-4 transition-all duration-500 ${workflowStatus === 'running' ? 'bg-gradient-to-r from-blue-500 to-blue-600' : workflowStatus === 'completed' ? 'bg-gradient-to-r from-green-500 to-green-600' : workflowStatus === 'failed' ? 'bg-gradient-to-r from-red-500 to-red-600' : 'bg-gradient-to-r from-gray-500 to-gray-600'} text-white`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\",\n              children: [workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(Loader2, {\n                className: \"w-5 h-5 animate-spin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 50\n              }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(CheckCircle, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 52\n              }, this), workflowStatus === 'failed' && /*#__PURE__*/_jsxDEV(XCircle, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 49\n              }, this), workflowStatus === 'idle' && /*#__PURE__*/_jsxDEV(Clock, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 47\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-semibold text-lg\",\n                children: \"Temu\\u6D41\\u91CF\\u52A0\\u901F\\u81EA\\u52A8\\u5316\\u4EFB\\u52A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-white/80 text-sm\",\n                children: [workflowStatus === 'idle' && '准备开始执行...', workflowStatus === 'running' && `正在执行第 ${currentStep + 1} 步，共 ${steps.length} 步`, workflowStatus === 'completed' && '任务执行完成', workflowStatus === 'failed' && `第 ${failedStep + 1} 步执行失败`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-white/80\",\n              children: \"\\u8FDB\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold\",\n              children: [Math.round((currentStep + 1) / steps.length * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 bg-blue-50 rounded-xl p-4 border border-blue-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-900\",\n              children: \"\\u6267\\u884C\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-700\",\n            children: \"\\u9AD8\\u7EA7\\u6D41\\u91CF\\u52A0\\u6743\\u6863\\u4F4D\\uFF0C\\u4EF7\\u683C\\u8303\\u56F44-6\\u7F8E\\u5143\\uFF0C\\u65F6\\u654830\\u5929\\uFF0C\\u9884\\u8BA1\\u5904\\u7406200\\u4E2A\\u5546\\u54C1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), workflowStatus !== 'idle' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this), steps.map((step, index) => {\n            const isActive = index === currentStep;\n            const isCompleted = step.status === 'completed';\n            const isFailed = step.status === 'failed';\n            const isRunning = step.status === 'running';\n            const isWaiting = step.status === 'waiting';\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative mb-8 last:mb-0\",\n              children: [index < steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute left-6 top-12 w-0.5 h-16 transition-all duration-700 ${isCompleted ? 'bg-green-500' : 'bg-gray-200'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-4 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 relative z-10\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-white rounded-full border-4 border-gray-200 absolute inset-0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 relative z-10 ${isCompleted ? 'bg-green-500 border-4 border-green-200 shadow-lg' : isFailed ? 'bg-red-500 border-4 border-red-200 shadow-lg' : isRunning ? 'bg-blue-500 border-4 border-blue-200 shadow-lg animate-pulse' : isWaiting ? 'bg-gray-100 border-4 border-gray-200' : 'bg-gray-100 border-4 border-gray-200'}`,\n                    children: [isCompleted && /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-6 h-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 43\n                    }, this), isFailed && /*#__PURE__*/_jsxDEV(XCircle, {\n                      className: \"w-6 h-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 40\n                    }, this), isRunning && /*#__PURE__*/_jsxDEV(Loader2, {\n                      className: \"w-6 h-6 text-white animate-spin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 41\n                    }, this), isWaiting && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-4 h-4 rounded-full bg-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 41\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0 pt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: `text-lg font-semibold transition-colors duration-300 ${isCompleted ? 'text-green-700' : isFailed ? 'text-red-700' : isRunning ? 'text-blue-700' : 'text-gray-500'}`,\n                        children: step.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 277,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-xs font-medium ${isCompleted ? 'bg-green-100 text-green-700' : isFailed ? 'bg-red-100 text-red-700' : isRunning ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-500'}`,\n                        children: [isCompleted && '已完成', isFailed && '执行失败', isRunning && '进行中', isWaiting && '等待执行']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 287,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 27\n                    }, this), isFailed && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: retryFromFailed,\n                      disabled: isRunning,\n                      className: \"mt-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"\\u91CD\\u8BD5\\u6B64\\u6B65\\u9AA4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-4 rounded-lg border transition-all duration-300 ${isCompleted ? 'bg-green-50 border-green-200' : isFailed ? 'bg-red-50 border-red-200' : isRunning ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-sm leading-relaxed ${isCompleted ? 'text-green-700' : isFailed ? 'text-red-700' : isRunning ? 'text-blue-700' : 'text-gray-600'}`,\n                      children: getStepDescription(step)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 27\n                    }, this), isFailed && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3 p-3 bg-red-100 border border-red-200 rounded-lg\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start space-x-2\",\n                        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                          className: \"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 333,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-red-800 text-sm font-medium\",\n                            children: \"\\u9519\\u8BEF\\u8BE6\\u60C5\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 335,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-red-700 text-sm mt-1\",\n                            children: \"\\u6267\\u884C\\u5931\\u8D25\\uFF0C\\u53EF\\u80FD\\u662F\\u7F51\\u7EDC\\u8FDE\\u63A5\\u95EE\\u9898\\u6216\\u7CFB\\u7EDF\\u7E41\\u5FD9\\u3002\\u70B9\\u51FB\\\"\\u91CD\\u8BD5\\u6B64\\u6B65\\u9AA4\\\"\\u7EE7\\u7EED\\u6267\\u884C\\uFF0C\\u5DF2\\u5B8C\\u6210\\u7684\\u6B65\\u9AA4\\u4E0D\\u4F1A\\u91CD\\u590D\\u6267\\u884C\\u3002\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 336,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 334,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 332,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 19\n            }, this);\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-8 pt-4 border-t border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [workflowStatus === 'idle' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: executeWorkflow,\n                disabled: isRunning,\n                className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Play, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5F00\\u59CB\\u6267\\u884C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"button\", {\n                disabled: true,\n                className: \"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u4E2D...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: resetWorkflow,\n                className: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u6267\\u884C\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this), workflowStatus === 'failed' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: retryFromFailed,\n                  disabled: isRunning,\n                  className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u91CD\\u8BD5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: resetWorkflow,\n                  className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\",\n                  children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), workflowStatus === 'running' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(Loader2, {\n                    className: \"w-4 h-4 text-white animate-spin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-blue-700 font-medium\",\n                  children: [\"\\u6B63\\u5728\\u6267\\u884C\\u7B2C \", currentStep + 1, \" \\u6B65\\uFF0C\\u5171 \", steps.length, \" \\u6B65\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetWorkflow,\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\",\n              title: \"\\u91CD\\u65B0\\u5F00\\u59CB\",\n              children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), workflowStatus === 'completed' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-green-700 font-semibold text-base mb-1\",\n                  children: \"\\uD83C\\uDF89 \\u81EA\\u52A8\\u5316\\u6267\\u884C\\u5B8C\\u6210\\uFF01\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-green-600 text-sm\",\n                  children: \"\\u6240\\u6709\\u5546\\u54C1\\u5DF2\\u6210\\u529F\\u914D\\u7F6E\\u6D41\\u91CF\\u52A0\\u901F\\uFF0C\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this), workflowStatus === 'failed' && failedStep !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"w-5 h-5 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-700 font-semibold text-base mb-1\",\n                  children: \"\\u6267\\u884C\\u4E2D\\u65AD\\uFF0C\\u9700\\u8981\\u5904\\u7406\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-red-600 text-sm\",\n                  children: [\"\\u7B2C \", failedStep + 1, \" \\u6B65\\u51FA\\u73B0\\u95EE\\u9898\\uFF0C\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-white/20\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-semibold text-gray-900 mb-3 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this), \"Demo\\u63A7\\u5236\\u9762\\u677F\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 mb-4 leading-relaxed\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium\",\n          children: \"\\u6F14\\u793A\\u7279\\u6027\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-flex items-center ml-2 space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 49\n            }, this), \"\\u52A8\\u6001\\u63CF\\u8FF0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 49\n            }, this), \"\\u65F6\\u95F4\\u7EBF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 49\n            }, this), \"\\u5931\\u8D25\\u91CD\\u8BD5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n              className: \"w-3 h-3 text-green-500 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 49\n            }, this), \"\\u5B8C\\u7F8E\\u8FDE\\u63A5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: executeWorkflow,\n          disabled: isRunning,\n          className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u6A21\\u62DF\\u6267\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetWorkflow,\n          className: \"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u91CD\\u7F6E\\u72B6\\u6001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            // 快速演示失败场景\n            setVisibleSteps(3);\n            setSteps(prev => prev.map((step, index) => {\n              if (index === 0) return {\n                ...step,\n                status: 'completed'\n              };\n              if (index === 1) return {\n                ...step,\n                status: 'completed'\n              };\n              if (index === 2) return {\n                ...step,\n                status: 'failed'\n              };\n              return step;\n            }));\n            setWorkflowStatus('failed');\n            setFailedStep(2);\n          },\n          className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u6F14\\u793A\\u5931\\u8D25\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(TemuWorkflowDemo, \"fMJvJHTAjkN4KVVgc8nhos3pTik=\");\n_c = TemuWorkflowDemo;\nexport default TemuWorkflowDemo;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowDemo\");", "map": {"version": 3, "names": ["React", "useState", "Play", "RefreshCw", "CheckCircle", "XCircle", "AlertCircle", "Loader2", "Clock", "jsxDEV", "_jsxDEV", "TemuWorkflowDemo", "_s", "currentStep", "setCurrentStep", "isRunning", "setIsRunning", "workflowStatus", "setWorkflowStatus", "failedStep", "setFailedStep", "executionResults", "setExecutionResults", "workflowSteps", "id", "name", "runningDescription", "completedDescription", "failedDescription", "duration", "status", "steps", "setSteps", "getStepDescription", "step", "generateExecutionReport", "successfulProducts", "failedProducts", "reason", "report", "totalProcessed", "successful", "failed", "totalCost", "slice", "executionTime", "executeWorkflow", "startStep", "prev", "map", "index", "i", "length", "Promise", "resolve", "setTimeout", "shouldFail", "Math", "random", "retryFromFailed", "resetWorkflow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "round", "isActive", "isCompleted", "isFailed", "isWaiting", "onClick", "disabled", "title", "setVisibleSteps", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Clock } from 'lucide-react';\n\nconst TemuWorkflowDemo = () => {\n  const [currentStep, setCurrentStep] = useState(-1); // -1表示未开始\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n  const [executionResults, setExecutionResults] = useState(null); // 执行结果详情\n\n  // 工作流步骤定义 - 增强版本，包含失败详情\n  const workflowSteps = [\n    {\n      id: 'login',\n      name: '登录验证',\n      runningDescription: '正在验证Temu账号登录状态...',\n      completedDescription: '已成功登录【Temu账号】名下的店铺1',\n      failedDescription: '登录验证失败，请检查账号状态',\n      duration: 2000,\n      status: 'waiting'\n    },\n    {\n      id: 'product_filter',\n      name: '商品筛选',\n      runningDescription: '正在筛选符合条件的商品...',\n      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n      failedDescription: '商品筛选失败，可能是网络连接问题',\n      duration: 3000,\n      status: 'waiting'\n    },\n    {\n      id: 'product_processing',\n      name: '商品加权处理',\n      runningDescription: '正在为商品设置流量加权配置...',\n      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',\n      failedDescription: '商品加权处理失败，部分商品无法配置',\n      duration: 4000,\n      status: 'waiting'\n    },\n    {\n      id: 'result_summary',\n      name: '结果汇总',\n      runningDescription: '正在生成执行结果报告...',\n      completedDescription: '任务执行完成，正在生成详细报告',\n      failedDescription: '结果汇总失败，无法生成完整报告',\n      duration: 1500,\n      status: 'waiting'\n    }\n  ];\n\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = (step) => {\n    switch (step.status) {\n      case 'running':\n        return step.runningDescription;\n      case 'completed':\n        return step.completedDescription;\n      case 'failed':\n        return step.failedDescription;\n      case 'waiting':\n        return '等待执行...';\n      default:\n        return '等待执行...';\n    }\n  };\n\n  // 生成详细的执行结果报告\n  const generateExecutionReport = () => {\n    const successfulProducts = [\n      '【智能手机壳-透明款】', '【蓝牙耳机-运动版】', '【充电宝-快充版】',\n      '【数据线-Type-C】', '【手机支架-桌面版】', '【无线充电器】'\n    ];\n\n    const failedProducts = [\n      { name: '【高端手机壳-奢华版】', reason: 'AI判断其价格为6.8美元，高于5美元的设定而被跳过' },\n      { name: '【专业摄影灯】', reason: '页面长时间无法打开而执行失败' }\n    ];\n\n    const report = {\n      totalProcessed: 200,\n      successful: 186,\n      failed: 14,\n      totalCost: 892.40,\n      successfulProducts: successfulProducts.slice(0, 6),\n      failedProducts: failedProducts,\n      executionTime: '8分32秒'\n    };\n\n    return report;\n  };\n\n  // 模拟工作流执行 - 重新设计的执行逻辑\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setExecutionResults(null);\n\n    // 从失败步骤开始，或从第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是全新开始，重置所有步骤状态\n    if (failedStep === null) {\n      setSteps(prev => prev.map(step => ({ ...step, status: 'waiting' })));\n    } else {\n      // 如果是重试，只重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) =>\n        index === failedStep ? { ...step, status: 'waiting' } : step\n      ));\n      setFailedStep(null);\n    }\n\n    // 执行工作流步骤\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n\n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'running' } : step\n      ));\n\n      // 模拟步骤执行时间\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 模拟随机失败（20%概率，主要在第2和第3步）\n      const shouldFail = Math.random() < 0.2 && (i === 1 || i === 2);\n\n      if (shouldFail) {\n        // 步骤失败\n        setSteps(prev => prev.map((step, index) =>\n          index === i ? { ...step, status: 'failed' } : step\n        ));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 步骤成功完成\n      setSteps(prev => prev.map((step, index) =>\n        index === i ? { ...step, status: 'completed' } : step\n      ));\n\n      // 短暂延迟，让用户看到状态变化\n      await new Promise(resolve => setTimeout(resolve, 300));\n    }\n\n    // 所有步骤完成，生成执行结果\n    const report = generateExecutionReport();\n    setExecutionResults(report);\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n    setCurrentStep(-1);\n  };\n\n  // 重试失败的步骤 - 精准重试机制\n  const retryFromFailed = () => {\n    if (failedStep !== null && !isRunning) {\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(-1);\n    setFailedStep(null);\n    setExecutionResults(null);\n    setSteps(workflowSteps.map(step => ({ ...step, status: 'waiting' })));\n  };\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      {/* 长任务执行监控面板 */}\n      <div className=\"bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/30\">\n        {/* 头部状态栏 */}\n        <div className={`p-4 transition-all duration-500 ${\n          workflowStatus === 'running' ? 'bg-gradient-to-r from-blue-500 to-blue-600' :\n          workflowStatus === 'completed' ? 'bg-gradient-to-r from-green-500 to-green-600' :\n          workflowStatus === 'failed' ? 'bg-gradient-to-r from-red-500 to-red-600' :\n          'bg-gradient-to-r from-gray-500 to-gray-600'\n        } text-white`}>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\">\n                {workflowStatus === 'running' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                {workflowStatus === 'completed' && <CheckCircle className=\"w-5 h-5\" />}\n                {workflowStatus === 'failed' && <XCircle className=\"w-5 h-5\" />}\n                {workflowStatus === 'idle' && <Clock className=\"w-5 h-5\" />}\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-lg\">Temu流量加速自动化任务</h3>\n                <p className=\"text-white/80 text-sm\">\n                  {workflowStatus === 'idle' && '准备开始执行...'}\n                  {workflowStatus === 'running' && `正在执行第 ${currentStep + 1} 步，共 ${steps.length} 步`}\n                  {workflowStatus === 'completed' && '任务执行完成'}\n                  {workflowStatus === 'failed' && `第 ${failedStep + 1} 步执行失败`}\n                </p>\n              </div>\n            </div>\n\n            {/* 进度指示器 */}\n            {workflowStatus === 'running' && (\n              <div className=\"text-right\">\n                <div className=\"text-sm text-white/80\">进度</div>\n                <div className=\"text-lg font-bold\">{Math.round(((currentStep + 1) / steps.length) * 100)}%</div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 主要内容区域 */}\n        <div className=\"p-6\">\n          {/* 任务配置信息 */}\n          <div className=\"mb-6 bg-blue-50 rounded-xl p-4 border border-blue-100\">\n            <div className=\"flex items-center mb-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></div>\n              <span className=\"font-medium text-gray-900\">执行配置</span>\n            </div>\n            <p className=\"text-sm text-gray-700\">高级流量加权档位，价格范围4-6美元，时效30天，预计处理200个商品</p>\n          </div>\n\n          {/* 重新设计的时间线 - 只在开始执行后显示 */}\n          {workflowStatus !== 'idle' && (\n            <div className=\"relative\">\n              {/* 主时间线 */}\n              <div className=\"absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200\"></div>\n\n              {steps.map((step, index) => {\n                const isActive = index === currentStep;\n                const isCompleted = step.status === 'completed';\n                const isFailed = step.status === 'failed';\n                const isRunning = step.status === 'running';\n                const isWaiting = step.status === 'waiting';\n\n                return (\n                  <div key={step.id} className=\"relative mb-8 last:mb-0\">\n                    {/* 连接线段 - 只有完成的步骤显示绿色 */}\n                    {index < steps.length - 1 && (\n                      <div\n                        className={`absolute left-6 top-12 w-0.5 h-16 transition-all duration-700 ${\n                          isCompleted ? 'bg-green-500' : 'bg-gray-200'\n                        }`}\n                      ></div>\n                    )}\n\n                    {/* 步骤容器 */}\n                    <div className=\"flex items-start space-x-4 relative\">\n                      {/* 状态图标 */}\n                      <div className=\"flex-shrink-0 relative z-10\">\n                        {/* 背景圆圈 */}\n                        <div className=\"w-12 h-12 bg-white rounded-full border-4 border-gray-200 absolute inset-0\"></div>\n\n                        {/* 状态图标 */}\n                        <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 relative z-10 ${\n                          isCompleted ? 'bg-green-500 border-4 border-green-200 shadow-lg' :\n                          isFailed ? 'bg-red-500 border-4 border-red-200 shadow-lg' :\n                          isRunning ? 'bg-blue-500 border-4 border-blue-200 shadow-lg animate-pulse' :\n                          isWaiting ? 'bg-gray-100 border-4 border-gray-200' :\n                          'bg-gray-100 border-4 border-gray-200'\n                        }`}>\n                          {isCompleted && <CheckCircle className=\"w-6 h-6 text-white\" />}\n                          {isFailed && <XCircle className=\"w-6 h-6 text-white\" />}\n                          {isRunning && <Loader2 className=\"w-6 h-6 text-white animate-spin\" />}\n                          {isWaiting && <div className=\"w-4 h-4 rounded-full bg-gray-400\"></div>}\n                        </div>\n                      </div>\n\n                      {/* 步骤内容 */}\n                      <div className=\"flex-1 min-w-0 pt-2\">\n                        <div className=\"mb-3\">\n                          <div className=\"flex items-center justify-between\">\n                            <h4 className={`text-lg font-semibold transition-colors duration-300 ${\n                              isCompleted ? 'text-green-700' :\n                              isFailed ? 'text-red-700' :\n                              isRunning ? 'text-blue-700' :\n                              'text-gray-500'\n                            }`}>\n                              {step.name}\n                            </h4>\n\n                            {/* 状态标签 */}\n                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                              isCompleted ? 'bg-green-100 text-green-700' :\n                              isFailed ? 'bg-red-100 text-red-700' :\n                              isRunning ? 'bg-blue-100 text-blue-700' :\n                              'bg-gray-100 text-gray-500'\n                            }`}>\n                              {isCompleted && '已完成'}\n                              {isFailed && '执行失败'}\n                              {isRunning && '进行中'}\n                              {isWaiting && '等待执行'}\n                            </span>\n                          </div>\n\n                          {/* 失败状态的重试按钮 */}\n                          {isFailed && (\n                            <button\n                              onClick={retryFromFailed}\n                              disabled={isRunning}\n                              className=\"mt-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                            >\n                              <RefreshCw className=\"w-4 h-4\" />\n                              <span>重试此步骤</span>\n                            </button>\n                          )}\n                        </div>\n\n                        {/* 步骤描述 */}\n                        <div className={`p-4 rounded-lg border transition-all duration-300 ${\n                          isCompleted ? 'bg-green-50 border-green-200' :\n                          isFailed ? 'bg-red-50 border-red-200' :\n                          isRunning ? 'bg-blue-50 border-blue-200' :\n                          'bg-gray-50 border-gray-200'\n                        }`}>\n                          <p className={`text-sm leading-relaxed ${\n                            isCompleted ? 'text-green-700' :\n                            isFailed ? 'text-red-700' :\n                            isRunning ? 'text-blue-700' :\n                            'text-gray-600'\n                          }`}>\n                            {getStepDescription(step)}\n                          </p>\n\n                          {/* 失败时的详细错误信息 */}\n                          {isFailed && (\n                            <div className=\"mt-3 p-3 bg-red-100 border border-red-200 rounded-lg\">\n                              <div className=\"flex items-start space-x-2\">\n                                <AlertCircle className=\"w-4 h-4 text-red-600 mt-0.5 flex-shrink-0\" />\n                                <div>\n                                  <p className=\"text-red-800 text-sm font-medium\">错误详情</p>\n                                  <p className=\"text-red-700 text-sm mt-1\">\n                                    执行失败，可能是网络连接问题或系统繁忙。点击\"重试此步骤\"继续执行，已完成的步骤不会重复执行。\n                                  </p>\n                                </div>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          )}\n\n          {/* 操作按钮区域 */}\n          <div className=\"mt-8 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                {workflowStatus === 'idle' && (\n                  <button\n                    onClick={executeWorkflow}\n                    disabled={isRunning}\n                    className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                  >\n                    <Play className=\"w-4 h-4\" />\n                    <span>开始执行</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'running' && (\n                  <button\n                    disabled\n                    className=\"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span>执行中...</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'completed' && (\n                  <button\n                    onClick={resetWorkflow}\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                  >\n                    <CheckCircle className=\"w-4 h-4\" />\n                    <span>执行完成</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'failed' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={retryFromFailed}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <RefreshCw className=\"w-4 h-4\" />\n                      <span>重试</span>\n                    </button>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\"\n                    >\n                      重新开始\n                    </button>\n                  </div>\n                )}\n\n                {/* 整体进度指示 */}\n                {workflowStatus === 'running' && (\n                  <div className=\"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\">\n                      <Loader2 className=\"w-4 h-4 text-white animate-spin\" />\n                    </div>\n                    <span className=\"text-sm text-blue-700 font-medium\">\n                      正在执行第 {currentStep + 1} 步，共 {steps.length} 步\n                    </span>\n                  </div>\n                )}\n              </div>\n\n              {/* 刷新按钮 */}\n              <button\n                onClick={resetWorkflow}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\"\n                title=\"重新开始\"\n              >\n                <RefreshCw className=\"w-4 h-4\" />\n              </button>\n            </div>\n\n            {/* 状态指示器 */}\n            {workflowStatus === 'completed' && (\n              <div className=\"mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <CheckCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-green-700 font-semibold text-base mb-1\">\n                      🎉 自动化执行完成！\n                    </div>\n                    <div className=\"text-green-600 text-sm\">\n                      所有商品已成功配置流量加速，系统运行正常\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {workflowStatus === 'failed' && failedStep !== null && (\n              <div className=\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-red-700 font-semibold text-base mb-1\">\n                      执行中断，需要处理\n                    </div>\n                    <div className=\"text-red-600 text-sm\">\n                      第 {failedStep + 1} 步出现问题，可点击重试继续执行\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Demo控制面板 */}\n      <div className=\"mt-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-white/20\">\n        <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center\">\n          <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></div>\n          Demo控制面板\n        </h4>\n        <p className=\"text-sm text-gray-600 mb-4 leading-relaxed\">\n          <span className=\"font-medium\">演示特性：</span>\n          <span className=\"inline-flex items-center ml-2 space-x-3\">\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />动态描述</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />时间线</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />失败重试</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />完美连接</span>\n          </span>\n        </p>\n        <div className=\"flex flex-wrap gap-3\">\n          <button\n            onClick={executeWorkflow}\n            disabled={isRunning}\n            className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>🚀</span>\n            <span>模拟执行</span>\n          </button>\n          <button\n            onClick={resetWorkflow}\n            className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>🔄</span>\n            <span>重置状态</span>\n          </button>\n          <button\n            onClick={() => {\n              // 快速演示失败场景\n              setVisibleSteps(3);\n              setSteps(prev => prev.map((step, index) => {\n                if (index === 0) return { ...step, status: 'completed' };\n                if (index === 1) return { ...step, status: 'completed' };\n                if (index === 2) return { ...step, status: 'failed' };\n                return step;\n              }));\n              setWorkflowStatus('failed');\n              setFailedStep(2);\n            }}\n            className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>⚠️</span>\n            <span>演示失败</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TemuWorkflowDemo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAEC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElG,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEhE;EACA,MAAMsB,aAAa,GAAG,CACpB;IACEC,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,mBAAmB;IACvCC,oBAAoB,EAAE,qBAAqB;IAC3CC,iBAAiB,EAAE,gBAAgB;IACnCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,gBAAgB;IACpCC,oBAAoB,EAAE,2CAA2C;IACjEC,iBAAiB,EAAE,kBAAkB;IACrCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,oBAAoB;IACxBC,IAAI,EAAE,QAAQ;IACdC,kBAAkB,EAAE,kBAAkB;IACtCC,oBAAoB,EAAE,2BAA2B;IACjDC,iBAAiB,EAAE,mBAAmB;IACtCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE,MAAM;IACZC,kBAAkB,EAAE,eAAe;IACnCC,oBAAoB,EAAE,iBAAiB;IACvCC,iBAAiB,EAAE,iBAAiB;IACpCC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAACsB,aAAa,CAAC;;EAEjD;EACA,MAAMU,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI,CAACJ,MAAM;MACjB,KAAK,SAAS;QACZ,OAAOI,IAAI,CAACR,kBAAkB;MAChC,KAAK,WAAW;QACd,OAAOQ,IAAI,CAACP,oBAAoB;MAClC,KAAK,QAAQ;QACX,OAAOO,IAAI,CAACN,iBAAiB;MAC/B,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;;EAED;EACA,MAAMO,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,kBAAkB,GAAG,CACzB,aAAa,EAAE,YAAY,EAAE,WAAW,EACxC,cAAc,EAAE,YAAY,EAAE,SAAS,CACxC;IAED,MAAMC,cAAc,GAAG,CACrB;MAAEZ,IAAI,EAAE,aAAa;MAAEa,MAAM,EAAE;IAA6B,CAAC,EAC7D;MAAEb,IAAI,EAAE,SAAS;MAAEa,MAAM,EAAE;IAAiB,CAAC,CAC9C;IAED,MAAMC,MAAM,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,UAAU,EAAE,GAAG;MACfC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,MAAM;MACjBP,kBAAkB,EAAEA,kBAAkB,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAClDP,cAAc,EAAEA,cAAc;MAC9BQ,aAAa,EAAE;IACjB,CAAC;IAED,OAAON,MAAM;EACf,CAAC;;EAED;EACA,MAAMO,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC9B,YAAY,CAAC,IAAI,CAAC;IAClBE,iBAAiB,CAAC,SAAS,CAAC;IAC5BI,mBAAmB,CAAC,IAAI,CAAC;;IAEzB;IACA,MAAMyB,SAAS,GAAG5B,UAAU,KAAK,IAAI,GAAGA,UAAU,GAAG,CAAC;IACtDL,cAAc,CAACiC,SAAS,CAAC;;IAEzB;IACA,IAAI5B,UAAU,KAAK,IAAI,EAAE;MACvBa,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACf,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC,MAAM;MACL;MACAE,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KACpCA,KAAK,KAAK/B,UAAU,GAAG;QAAE,GAAGe,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,GAAGI,IAC1D,CAAC,CAAC;MACFd,aAAa,CAAC,IAAI,CAAC;IACrB;;IAEA;IACA,KAAK,IAAI+B,CAAC,GAAGJ,SAAS,EAAEI,CAAC,GAAGpB,KAAK,CAACqB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7CrC,cAAc,CAACqC,CAAC,CAAC;;MAEjB;MACAnB,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KACpCA,KAAK,KAAKC,CAAC,GAAG;QAAE,GAAGjB,IAAI;QAAEJ,MAAM,EAAE;MAAU,CAAC,GAAGI,IACjD,CAAC,CAAC;;MAEF;MACA,MAAM,IAAImB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEvB,KAAK,CAACoB,CAAC,CAAC,CAACtB,QAAQ,CAAC,CAAC;;MAEpE;MACA,MAAM2B,UAAU,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,KAAKP,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC;MAE9D,IAAIK,UAAU,EAAE;QACd;QACAxB,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KACpCA,KAAK,KAAKC,CAAC,GAAG;UAAE,GAAGjB,IAAI;UAAEJ,MAAM,EAAE;QAAS,CAAC,GAAGI,IAChD,CAAC,CAAC;QACFhB,iBAAiB,CAAC,QAAQ,CAAC;QAC3BE,aAAa,CAAC+B,CAAC,CAAC;QAChBnC,YAAY,CAAC,KAAK,CAAC;QACnB;MACF;;MAEA;MACAgB,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KACpCA,KAAK,KAAKC,CAAC,GAAG;QAAE,GAAGjB,IAAI;QAAEJ,MAAM,EAAE;MAAY,CAAC,GAAGI,IACnD,CAAC,CAAC;;MAEF;MACA,MAAM,IAAImB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IACxD;;IAEA;IACA,MAAMf,MAAM,GAAGJ,uBAAuB,CAAC,CAAC;IACxCb,mBAAmB,CAACiB,MAAM,CAAC;IAC3BrB,iBAAiB,CAAC,WAAW,CAAC;IAC9BE,aAAa,CAAC,IAAI,CAAC;IACnBJ,YAAY,CAAC,KAAK,CAAC;IACnBF,cAAc,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAM6C,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIxC,UAAU,KAAK,IAAI,IAAI,CAACJ,SAAS,EAAE;MACrC+B,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1B5C,YAAY,CAAC,KAAK,CAAC;IACnBE,iBAAiB,CAAC,MAAM,CAAC;IACzBJ,cAAc,CAAC,CAAC,CAAC,CAAC;IAClBM,aAAa,CAAC,IAAI,CAAC;IACnBE,mBAAmB,CAAC,IAAI,CAAC;IACzBU,QAAQ,CAACT,aAAa,CAAC0B,GAAG,CAACf,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEJ,MAAM,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC;EACvE,CAAC;EAED,oBACEpB,OAAA;IAAKmD,SAAS,EAAC,0BAA0B;IAAAC,QAAA,gBAEvCpD,OAAA;MAAKmD,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBAExGpD,OAAA;QAAKmD,SAAS,EAAE,mCACd5C,cAAc,KAAK,SAAS,GAAG,4CAA4C,GAC3EA,cAAc,KAAK,WAAW,GAAG,8CAA8C,GAC/EA,cAAc,KAAK,QAAQ,GAAG,0CAA0C,GACxE,4CAA4C,aAChC;QAAA6C,QAAA,eACZpD,OAAA;UAAKmD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDpD,OAAA;YAAKmD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CpD,OAAA;cAAKmD,SAAS,EAAC,qEAAqE;cAAAC,QAAA,GACjF7C,cAAc,KAAK,SAAS,iBAAIP,OAAA,CAACH,OAAO;gBAACsD,SAAS,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC5EjD,cAAc,KAAK,WAAW,iBAAIP,OAAA,CAACN,WAAW;gBAACyD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACrEjD,cAAc,KAAK,QAAQ,iBAAIP,OAAA,CAACL,OAAO;gBAACwD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC9DjD,cAAc,KAAK,MAAM,iBAAIP,OAAA,CAACF,KAAK;gBAACqD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNxD,OAAA;cAAAoD,QAAA,gBACEpD,OAAA;gBAAImD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDxD,OAAA;gBAAGmD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GACjC7C,cAAc,KAAK,MAAM,IAAI,WAAW,EACxCA,cAAc,KAAK,SAAS,IAAI,SAASJ,WAAW,GAAG,CAAC,QAAQkB,KAAK,CAACqB,MAAM,IAAI,EAChFnC,cAAc,KAAK,WAAW,IAAI,QAAQ,EAC1CA,cAAc,KAAK,QAAQ,IAAI,KAAKE,UAAU,GAAG,CAAC,QAAQ;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLjD,cAAc,KAAK,SAAS,iBAC3BP,OAAA;YAAKmD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBpD,OAAA;cAAKmD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/CxD,OAAA;cAAKmD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAEL,IAAI,CAACU,KAAK,CAAE,CAACtD,WAAW,GAAG,CAAC,IAAIkB,KAAK,CAACqB,MAAM,GAAI,GAAG,CAAC,EAAC,GAAC;YAAA;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxD,OAAA;QAAKmD,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAElBpD,OAAA;UAAKmD,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACpEpD,OAAA;YAAKmD,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCpD,OAAA;cAAKmD,SAAS,EAAC;YAAuC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7DxD,OAAA;cAAMmD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNxD,OAAA;YAAGmD,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,EAGLjD,cAAc,KAAK,MAAM,iBACxBP,OAAA;UAAKmD,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAEvBpD,OAAA;YAAKmD,SAAS,EAAC;UAAkD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAEvEnC,KAAK,CAACkB,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KAAK;YAC1B,MAAMkB,QAAQ,GAAGlB,KAAK,KAAKrC,WAAW;YACtC,MAAMwD,WAAW,GAAGnC,IAAI,CAACJ,MAAM,KAAK,WAAW;YAC/C,MAAMwC,QAAQ,GAAGpC,IAAI,CAACJ,MAAM,KAAK,QAAQ;YACzC,MAAMf,SAAS,GAAGmB,IAAI,CAACJ,MAAM,KAAK,SAAS;YAC3C,MAAMyC,SAAS,GAAGrC,IAAI,CAACJ,MAAM,KAAK,SAAS;YAE3C,oBACEpB,OAAA;cAAmBmD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,GAEnDZ,KAAK,GAAGnB,KAAK,CAACqB,MAAM,GAAG,CAAC,iBACvB1C,OAAA;gBACEmD,SAAS,EAAE,iEACTQ,WAAW,GAAG,cAAc,GAAG,aAAa;cAC3C;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACP,eAGDxD,OAAA;gBAAKmD,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAElDpD,OAAA;kBAAKmD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAE1CpD,OAAA;oBAAKmD,SAAS,EAAC;kBAA2E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAGjGxD,OAAA;oBAAKmD,SAAS,EAAE,qGACdQ,WAAW,GAAG,kDAAkD,GAChEC,QAAQ,GAAG,8CAA8C,GACzDvD,SAAS,GAAG,8DAA8D,GAC1EwD,SAAS,GAAG,sCAAsC,GAClD,sCAAsC,EACrC;oBAAAT,QAAA,GACAO,WAAW,iBAAI3D,OAAA,CAACN,WAAW;sBAACyD,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAC7DI,QAAQ,iBAAI5D,OAAA,CAACL,OAAO;sBAACwD,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtDnD,SAAS,iBAAIL,OAAA,CAACH,OAAO;sBAACsD,SAAS,EAAC;oBAAiC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACpEK,SAAS,iBAAI7D,OAAA;sBAAKmD,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNxD,OAAA;kBAAKmD,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClCpD,OAAA;oBAAKmD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBpD,OAAA;sBAAKmD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDpD,OAAA;wBAAImD,SAAS,EAAE,wDACbQ,WAAW,GAAG,gBAAgB,GAC9BC,QAAQ,GAAG,cAAc,GACzBvD,SAAS,GAAG,eAAe,GAC3B,eAAe,EACd;wBAAA+C,QAAA,EACA5B,IAAI,CAACT;sBAAI;wBAAAsC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC,eAGLxD,OAAA;wBAAMmD,SAAS,EAAE,8CACfQ,WAAW,GAAG,6BAA6B,GAC3CC,QAAQ,GAAG,yBAAyB,GACpCvD,SAAS,GAAG,2BAA2B,GACvC,2BAA2B,EAC1B;wBAAA+C,QAAA,GACAO,WAAW,IAAI,KAAK,EACpBC,QAAQ,IAAI,MAAM,EAClBvD,SAAS,IAAI,KAAK,EAClBwD,SAAS,IAAI,MAAM;sBAAA;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,EAGLI,QAAQ,iBACP5D,OAAA;sBACE8D,OAAO,EAAEb,eAAgB;sBACzBc,QAAQ,EAAE1D,SAAU;sBACpB8C,SAAS,EAAC,uTAAuT;sBAAAC,QAAA,gBAEjUpD,OAAA,CAACP,SAAS;wBAAC0D,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjCxD,OAAA;wBAAAoD,QAAA,EAAM;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAGNxD,OAAA;oBAAKmD,SAAS,EAAE,qDACdQ,WAAW,GAAG,8BAA8B,GAC5CC,QAAQ,GAAG,0BAA0B,GACrCvD,SAAS,GAAG,4BAA4B,GACxC,4BAA4B,EAC3B;oBAAA+C,QAAA,gBACDpD,OAAA;sBAAGmD,SAAS,EAAE,2BACZQ,WAAW,GAAG,gBAAgB,GAC9BC,QAAQ,GAAG,cAAc,GACzBvD,SAAS,GAAG,eAAe,GAC3B,eAAe,EACd;sBAAA+C,QAAA,EACA7B,kBAAkB,CAACC,IAAI;oBAAC;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,EAGHI,QAAQ,iBACP5D,OAAA;sBAAKmD,SAAS,EAAC,sDAAsD;sBAAAC,QAAA,eACnEpD,OAAA;wBAAKmD,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,gBACzCpD,OAAA,CAACJ,WAAW;0BAACuD,SAAS,EAAC;wBAA2C;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACrExD,OAAA;0BAAAoD,QAAA,gBACEpD,OAAA;4BAAGmD,SAAS,EAAC,kCAAkC;4BAAAC,QAAA,EAAC;0BAAI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC,eACxDxD,OAAA;4BAAGmD,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,EAAC;0BAEzC;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAxGEhC,IAAI,CAACV,EAAE;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyGZ,CAAC;UAEV,CAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDxD,OAAA;UAAKmD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBACjDpD,OAAA;YAAKmD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDpD,OAAA;cAAKmD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GACzC7C,cAAc,KAAK,MAAM,iBACxBP,OAAA;gBACE8D,OAAO,EAAE1B,eAAgB;gBACzB2B,QAAQ,EAAE1D,SAAU;gBACpB8C,SAAS,EAAC,0SAA0S;gBAAAC,QAAA,gBAEpTpD,OAAA,CAACR,IAAI;kBAAC2D,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BxD,OAAA;kBAAAoD,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT,EAEAjD,cAAc,KAAK,SAAS,iBAC3BP,OAAA;gBACE+D,QAAQ;gBACRZ,SAAS,EAAC,+IAA+I;gBAAAC,QAAA,gBAEzJpD,OAAA,CAACH,OAAO;kBAACsD,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CxD,OAAA;kBAAAoD,QAAA,EAAM;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACT,EAEAjD,cAAc,KAAK,WAAW,iBAC7BP,OAAA;gBACE8D,OAAO,EAAEZ,aAAc;gBACvBC,SAAS,EAAC,8PAA8P;gBAAAC,QAAA,gBAExQpD,OAAA,CAACN,WAAW;kBAACyD,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnCxD,OAAA;kBAAAoD,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACT,EAEAjD,cAAc,KAAK,QAAQ,iBAC1BP,OAAA;gBAAKmD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CpD,OAAA;kBACE8D,OAAO,EAAEb,eAAgB;kBACzBc,QAAQ,EAAE1D,SAAU;kBACpB8C,SAAS,EAAC,kTAAkT;kBAAAC,QAAA,gBAE5TpD,OAAA,CAACP,SAAS;oBAAC0D,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjCxD,OAAA;oBAAAoD,QAAA,EAAM;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACTxD,OAAA;kBACE8D,OAAO,EAAEZ,aAAc;kBACvBC,SAAS,EAAC,8NAA8N;kBAAAC,QAAA,EACzO;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAGAjD,cAAc,KAAK,SAAS,iBAC3BP,OAAA;gBAAKmD,SAAS,EAAC,+HAA+H;gBAAAC,QAAA,gBAC5IpD,OAAA;kBAAKmD,SAAS,EAAC,kGAAkG;kBAAAC,QAAA,eAC/GpD,OAAA,CAACH,OAAO;oBAACsD,SAAS,EAAC;kBAAiC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACNxD,OAAA;kBAAMmD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAAC,iCAC5C,EAACjD,WAAW,GAAG,CAAC,EAAC,sBAAK,EAACkB,KAAK,CAACqB,MAAM,EAAC,SAC5C;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNxD,OAAA;cACE8D,OAAO,EAAEZ,aAAc;cACvBC,SAAS,EAAC,gHAAgH;cAC1Ha,KAAK,EAAC,0BAAM;cAAAZ,QAAA,eAEZpD,OAAA,CAACP,SAAS;gBAAC0D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGLjD,cAAc,KAAK,WAAW,iBAC7BP,OAAA;YAAKmD,SAAS,EAAC,uGAAuG;YAAAC,QAAA,eACpHpD,OAAA;cAAKmD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CpD,OAAA;gBAAKmD,SAAS,EAAC,kHAAkH;gBAAAC,QAAA,eAC/HpD,OAAA,CAACN,WAAW;kBAACyD,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNxD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAKmD,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,EAAC;gBAE7D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxD,OAAA;kBAAKmD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAC;gBAExC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEAjD,cAAc,KAAK,QAAQ,IAAIE,UAAU,KAAK,IAAI,iBACjDT,OAAA;YAAKmD,SAAS,EAAC,kGAAkG;YAAAC,QAAA,eAC/GpD,OAAA;cAAKmD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CpD,OAAA;gBAAKmD,SAAS,EAAC,+GAA+G;gBAAAC,QAAA,eAC5HpD,OAAA,CAACJ,WAAW;kBAACuD,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNxD,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAKmD,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxD,OAAA;kBAAKmD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,GAAC,SAClC,EAAC3C,UAAU,GAAG,CAAC,EAAC,6FACpB;gBAAA;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAKmD,SAAS,EAAC,iGAAiG;MAAAC,QAAA,gBAC9GpD,OAAA;QAAImD,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAChEpD,OAAA;UAAKmD,SAAS,EAAC;QAAuC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gCAE/D;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLxD,OAAA;QAAGmD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACvDpD,OAAA;UAAMmD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CxD,OAAA;UAAMmD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACvDpD,OAAA;YAAMmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAACpD,OAAA,CAACN,WAAW;cAACyD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtGxD,OAAA;YAAMmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAACpD,OAAA,CAACN,WAAW;cAACyD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBAAG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrGxD,OAAA;YAAMmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAACpD,OAAA,CAACN,WAAW;cAACyD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtGxD,OAAA;YAAMmD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAACpD,OAAA,CAACN,WAAW;cAACyD,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACJxD,OAAA;QAAKmD,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCpD,OAAA;UACE8D,OAAO,EAAE1B,eAAgB;UACzB2B,QAAQ,EAAE1D,SAAU;UACpB8C,SAAS,EAAC,qRAAqR;UAAAC,QAAA,gBAE/RpD,OAAA;YAAAoD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfxD,OAAA;YAAAoD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACTxD,OAAA;UACE8D,OAAO,EAAEZ,aAAc;UACvBC,SAAS,EAAC,qOAAqO;UAAAC,QAAA,gBAE/OpD,OAAA;YAAAoD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfxD,OAAA;YAAAoD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACTxD,OAAA;UACE8D,OAAO,EAAEA,CAAA,KAAM;YACb;YACAG,eAAe,CAAC,CAAC,CAAC;YAClB3C,QAAQ,CAACgB,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACf,IAAI,EAAEgB,KAAK,KAAK;cACzC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGhB,IAAI;gBAAEJ,MAAM,EAAE;cAAY,CAAC;cACxD,IAAIoB,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGhB,IAAI;gBAAEJ,MAAM,EAAE;cAAY,CAAC;cACxD,IAAIoB,KAAK,KAAK,CAAC,EAAE,OAAO;gBAAE,GAAGhB,IAAI;gBAAEJ,MAAM,EAAE;cAAS,CAAC;cACrD,OAAOI,IAAI;YACb,CAAC,CAAC,CAAC;YACHhB,iBAAiB,CAAC,QAAQ,CAAC;YAC3BE,aAAa,CAAC,CAAC,CAAC;UAClB,CAAE;UACFyC,SAAS,EAAC,6OAA6O;UAAAC,QAAA,gBAEvPpD,OAAA;YAAAoD,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACfxD,OAAA;YAAAoD,QAAA,EAAM;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAtgBID,gBAAgB;AAAAiE,EAAA,GAAhBjE,gBAAgB;AAwgBtB,eAAeA,gBAAgB;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}