{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx\";\nimport React from 'react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemuWorkflowShowcase = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: /*#__PURE__*/_jsxDEV(TemuWorkflowDemo, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = TemuWorkflowShowcase;\nexport default TemuWorkflowShowcase;\nvar _c;\n$RefreshReg$(_c, \"TemuWorkflowShowcase\");", "map": {"version": 3, "names": ["React", "TemuWorkflowDemo", "jsxDEV", "_jsxDEV", "TemuWorkflowShowcase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx"], "sourcesContent": ["import React from 'react';\nimport TemuWorkflowDemo from './TemuWorkflowDemo';\n\nconst TemuWorkflowShowcase = () => {\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6\">\n      <div className=\"max-w-4xl mx-auto\">\n        <TemuWorkflowDemo />\n      </div>\n    </div>\n  );\n};\n\n\nexport default TemuWorkflowShowcase;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAEjC,oBACED,OAAA;IAAKE,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFH,OAAA;MAAKE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChCH,OAAA,CAACF,gBAAgB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GATIP,oBAAoB;AAY1B,eAAeA,oBAAoB;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}