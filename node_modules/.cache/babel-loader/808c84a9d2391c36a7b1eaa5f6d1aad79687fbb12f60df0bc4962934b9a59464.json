{"ast": null, "code": "import React,{useState}from'react';import{Play,RefreshCw,CheckCircle,XCircle,AlertCircle,Loader2}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TemuWorkflowDemo=()=>{const[currentStep,setCurrentStep]=useState(0);const[isRunning,setIsRunning]=useState(false);const[workflowStatus,setWorkflowStatus]=useState('idle');// idle, running, completed, failed\nconst[visibleSteps,setVisibleSteps]=useState(1);// 控制显示的步骤数量\nconst[failedStep,setFailedStep]=useState(null);// 记录失败的步骤\n// 工作流步骤定义\nconst workflowSteps=[{id:'login',name:'登录',runningDescription:'正在验证登录信息...',completedDescription:'已完成登录【Temu账号】名下的店铺1',duration:2000,status:'pending'},{id:'product_filter',name:'商品筛选',runningDescription:'正在筛选符合条件的商品...',completedDescription:'已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',duration:3000,status:'pending'},{id:'product_processing',name:'商品加权',runningDescription:'正在为商品设置流量加权...',completedDescription:'已完成对筛选出的商品，设置30天的普通流量加速加权',duration:4000,status:'pending'},{id:'result',name:'结果',runningDescription:'正在生成执行结果...',completedDescription:'执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',duration:1000,status:'pending'}];const[steps,setSteps]=useState(workflowSteps);// 获取步骤描述\nconst getStepDescription=step=>{if(step.status==='running'){return step.runningDescription;}else if(step.status==='completed'){return step.completedDescription;}else if(step.status==='failed'){return`执行失败：${step.runningDescription}`;}return'等待执行...';};// 获取连接线颜色 - 修复逻辑：只有当前步骤完成后才显示绿色连接线\nconst getConnectionLineColor=stepIndex=>{const step=steps[stepIndex];if(step.status==='completed'){return'bg-green-500';}else if(step.status==='failed'){return'bg-red-500';}else if(step.status==='running'){return'bg-blue-500';}return'bg-gray-300';};// 模拟工作流执行\nconst executeWorkflow=async()=>{setIsRunning(true);setWorkflowStatus('running');setFailedStep(null);// 从当前失败步骤或第一步开始\nconst startStep=failedStep!==null?failedStep:0;setCurrentStep(startStep);// 如果是重新开始，重置步骤状态\nif(failedStep===null){setVisibleSteps(1);setSteps(prev=>prev.map(step=>({...step,status:'pending'})));}for(let i=startStep;i<steps.length;i++){setCurrentStep(i);// 显示当前步骤（如果还未显示）\nif(i+1>visibleSteps){setVisibleSteps(i+1);await new Promise(resolve=>setTimeout(resolve,300));// 短暂延迟显示步骤\n}// 设置当前步骤为运行中\nsetSteps(prev=>prev.map((step,index)=>index===i?{...step,status:'running'}:step));// 等待步骤完成\nawait new Promise(resolve=>setTimeout(resolve,steps[i].duration));// 随机失败演示（15%概率，在第2或第3步）\nconst shouldFail=Math.random()<0.15&&(i===1||i===2);if(shouldFail){setSteps(prev=>prev.map((step,index)=>index===i?{...step,status:'failed'}:step));setWorkflowStatus('failed');setFailedStep(i);setIsRunning(false);return;}// 设置步骤为完成\nsetSteps(prev=>prev.map((step,index)=>index===i?{...step,status:'completed'}:step));// 完成一步后显示下一步（如果不是最后一步）\nif(i<steps.length-1){setVisibleSteps(i+2);await new Promise(resolve=>setTimeout(resolve,500));// 延迟显示下一步\n}}setWorkflowStatus('completed');setFailedStep(null);setIsRunning(false);};// 重试失败的步骤\nconst retryFromFailed=()=>{if(failedStep!==null){// 重置失败步骤的状态\nsetSteps(prev=>prev.map((step,index)=>index===failedStep?{...step,status:'pending'}:step));executeWorkflow();}};// 重置整个工作流\nconst resetWorkflow=()=>{setIsRunning(false);setWorkflowStatus('idle');setCurrentStep(0);setVisibleSteps(1);setFailedStep(null);setSteps(workflowSteps.map(step=>({...step,status:'pending'})));};return/*#__PURE__*/_jsxs(\"div\",{className:\"w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-500 text-white p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-bold\",children:\"AI\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-semibold\",children:\"Temu\\u81EA\\u52A8\\u5316\\u52A9\\u624B\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-blue-100 text-sm\",children:\"\\u6D41\\u91CF\\u52A0\\u901F\\u81EA\\u52A8\\u5316\\u6267\\u884C\\u4E2D...\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-100 rounded-lg p-4 mb-4\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-800 mb-2\",children:\"\\u597D\\u7684\\uFF0C\\u5F00\\u59CB\\u6267\\u884C\\u8BA1\\u5212\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-600 bg-white rounded-lg p-3 border\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"\\u6267\\u884C\\u914D\\u7F6E\\uFF1A\"}),\"\\u9AD8\\u7EA7\\u6D41\\u91CF\\u52A0\\u6743\\u6863\\u4F4D\\uFF0C\\u4EF7\\u683C\\u8303\\u56F44-6\\u7F8E\\u5143\\uFF0C\\u65F6\\u654830\\u5929\"]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"relative\",children:steps.slice(0,visibleSteps).map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[index<visibleSteps-1&&/*#__PURE__*/_jsx(\"div\",{className:`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${getConnectionLineColor(index)}`,style:{height:'calc(100% + 1.5rem)'// 精确延伸到下一个步骤的图标位置\n}}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4 relative z-10 pb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-shrink-0 relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"}),/*#__PURE__*/_jsxs(\"div\",{className:`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${step.status==='completed'?'bg-green-500 text-white':step.status==='failed'?'bg-red-500 text-white':step.status==='running'?'bg-blue-500 text-white':'bg-gray-300 text-gray-600'}`,children:[step.status==='completed'&&/*#__PURE__*/_jsx(CheckCircle,{className:\"w-5 h-5\"}),step.status==='failed'&&/*#__PURE__*/_jsx(XCircle,{className:\"w-5 h-5\"}),step.status==='running'&&/*#__PURE__*/_jsx(Loader2,{className:\"w-5 h-5 animate-spin\"}),step.status==='pending'&&/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 rounded-full bg-gray-600\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0 pt-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-lg font-medium text-gray-900\",children:step.name}),step.status==='failed'&&/*#__PURE__*/_jsxs(\"button\",{onClick:retryFromFailed,disabled:isRunning,className:\"mt-2 text-xs bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1.5 rounded-full font-medium transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"w-3 h-3\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u91CD\\u8BD5\"})]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 leading-relaxed\",children:getStepDescription(step)}),step.status==='failed'&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-2 bg-red-50 border border-red-200 rounded-lg p-3\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-red-700 text-sm\",children:\"\\u6267\\u884C\\u5931\\u8D25\\uFF0C\\u53EF\\u80FD\\u662F\\u7F51\\u7EDC\\u8FDE\\u63A5\\u95EE\\u9898\\u6216\\u7CFB\\u7EDF\\u7E41\\u5FD9\\uFF0C\\u8BF7\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\\u3002\"})})]})]})]},step.id))}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-8 pt-4 border-t border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[workflowStatus==='idle'&&/*#__PURE__*/_jsxs(\"button\",{onClick:executeWorkflow,disabled:isRunning,className:\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Play,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u5F00\\u59CB\\u6267\\u884C\"})]}),workflowStatus==='running'&&/*#__PURE__*/_jsxs(\"button\",{disabled:true,className:\"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Loader2,{className:\"w-4 h-4 animate-spin\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6267\\u884C\\u4E2D...\"})]}),workflowStatus==='completed'&&/*#__PURE__*/_jsxs(\"button\",{onClick:resetWorkflow,className:\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6267\\u884C\\u5B8C\\u6210\"})]}),workflowStatus==='failed'&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:retryFromFailed,disabled:isRunning,className:\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"w-4 h-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u91CD\\u8BD5\"})]}),/*#__PURE__*/_jsx(\"button\",{onClick:resetWorkflow,className:\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\",children:\"\\u91CD\\u65B0\\u5F00\\u59CB\"})]}),workflowStatus==='running'&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\",children:/*#__PURE__*/_jsx(Loader2,{className:\"w-4 h-4 text-white animate-spin\"})}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-blue-700 font-medium\",children:[\"\\u6B63\\u5728\\u6267\\u884C\\u7B2C \",currentStep+1,\" \\u6B65\\uFF0C\\u5171 \",steps.length,\" \\u6B65\"]})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:resetWorkflow,className:\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\",title:\"\\u91CD\\u65B0\\u5F00\\u59CB\",children:/*#__PURE__*/_jsx(RefreshCw,{className:\"w-4 h-4\"})})]}),workflowStatus==='completed'&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\",children:/*#__PURE__*/_jsx(CheckCircle,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-green-700 font-semibold text-base mb-1\",children:\"\\uD83C\\uDF89 \\u81EA\\u52A8\\u5316\\u6267\\u884C\\u5B8C\\u6210\\uFF01\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-green-600 text-sm\",children:\"\\u6240\\u6709\\u5546\\u54C1\\u5DF2\\u6210\\u529F\\u914D\\u7F6E\\u6D41\\u91CF\\u52A0\\u901F\\uFF0C\\u7CFB\\u7EDF\\u8FD0\\u884C\\u6B63\\u5E38\"})]})]})}),workflowStatus==='failed'&&failedStep!==null&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\",children:/*#__PURE__*/_jsx(AlertCircle,{className:\"w-5 h-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-red-700 font-semibold text-base mb-1\",children:\"\\u6267\\u884C\\u4E2D\\u65AD\\uFF0C\\u9700\\u8981\\u5904\\u7406\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-red-600 text-sm\",children:[\"\\u7B2C \",failedStep+1,\" \\u6B65\\u51FA\\u73B0\\u95EE\\u9898\\uFF0C\\u53EF\\u70B9\\u51FB\\u91CD\\u8BD5\\u7EE7\\u7EED\\u6267\\u884C\"]})]})]})})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-white/20\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"font-semibold text-gray-900 mb-3 flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-blue-500 rounded-full mr-2\"}),\"Demo\\u63A7\\u5236\\u9762\\u677F\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600 mb-4 leading-relaxed\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:\"\\u6F14\\u793A\\u7279\\u6027\\uFF1A\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"inline-flex items-center ml-2 space-x-3\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-3 h-3 text-green-500 mr-1\"}),\"\\u52A8\\u6001\\u63CF\\u8FF0\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-3 h-3 text-green-500 mr-1\"}),\"\\u65F6\\u95F4\\u7EBF\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-3 h-3 text-green-500 mr-1\"}),\"\\u5931\\u8D25\\u91CD\\u8BD5\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(CheckCircle,{className:\"w-3 h-3 text-green-500 mr-1\"}),\"\\u5B8C\\u7F8E\\u8FDE\\u63A5\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap gap-3\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:executeWorkflow,disabled:isRunning,className:\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDE80\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6A21\\u62DF\\u6267\\u884C\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:resetWorkflow,className:\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDD04\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u91CD\\u7F6E\\u72B6\\u6001\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// 快速演示失败场景\nsetVisibleSteps(3);setSteps(prev=>prev.map((step,index)=>{if(index===0)return{...step,status:'completed'};if(index===1)return{...step,status:'completed'};if(index===2)return{...step,status:'failed'};return step;}));setWorkflowStatus('failed');setFailedStep(2);},className:\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6F14\\u793A\\u5931\\u8D25\"})]})]})]})]});};export default TemuWorkflowDemo;", "map": {"version": 3, "names": ["React", "useState", "Play", "RefreshCw", "CheckCircle", "XCircle", "AlertCircle", "Loader2", "jsx", "_jsx", "jsxs", "_jsxs", "TemuWorkflowDemo", "currentStep", "setCurrentStep", "isRunning", "setIsRunning", "workflowStatus", "setWorkflowStatus", "visibleSteps", "setVisibleSteps", "failedStep", "setFailedStep", "workflowSteps", "id", "name", "runningDescription", "completedDescription", "duration", "status", "steps", "setSteps", "getStepDescription", "step", "getConnectionLineColor", "stepIndex", "executeWorkflow", "startStep", "prev", "map", "i", "length", "Promise", "resolve", "setTimeout", "index", "shouldFail", "Math", "random", "retryFromFailed", "resetWorkflow", "className", "children", "slice", "style", "height", "onClick", "disabled", "title"], "sources": ["/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2 } from 'lucide-react';\n\nconst TemuWorkflowDemo = () => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isRunning, setIsRunning] = useState(false);\n  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed\n  const [visibleSteps, setVisibleSteps] = useState(1); // 控制显示的步骤数量\n  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤\n\n  // 工作流步骤定义\n  const workflowSteps = [\n    {\n      id: 'login',\n      name: '登录',\n      runningDescription: '正在验证登录信息...',\n      completedDescription: '已完成登录【Temu账号】名下的店铺1',\n      duration: 2000,\n      status: 'pending'\n    },\n    {\n      id: 'product_filter',\n      name: '商品筛选', \n      runningDescription: '正在筛选符合条件的商品...',\n      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',\n      duration: 3000,\n      status: 'pending'\n    },\n    {\n      id: 'product_processing',\n      name: '商品加权',\n      runningDescription: '正在为商品设置流量加权...',\n      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',\n      duration: 4000,\n      status: 'pending'\n    },\n    {\n      id: 'result',\n      name: '结果',\n      runningDescription: '正在生成执行结果...',\n      completedDescription: '执行完成！共处理200个商品，成功加速186个，跳过14个，总费用$892.40',\n      duration: 1000,\n      status: 'pending'\n    }\n  ];\n\n  const [steps, setSteps] = useState(workflowSteps);\n\n  // 获取步骤描述\n  const getStepDescription = (step) => {\n    if (step.status === 'running') {\n      return step.runningDescription;\n    } else if (step.status === 'completed') {\n      return step.completedDescription;\n    } else if (step.status === 'failed') {\n      return `执行失败：${step.runningDescription}`;\n    }\n    return '等待执行...';\n  };\n\n  // 获取连接线颜色 - 修复逻辑：只有当前步骤完成后才显示绿色连接线\n  const getConnectionLineColor = (stepIndex) => {\n    const step = steps[stepIndex];\n    if (step.status === 'completed') {\n      return 'bg-green-500';\n    } else if (step.status === 'failed') {\n      return 'bg-red-500';\n    } else if (step.status === 'running') {\n      return 'bg-blue-500';\n    }\n    return 'bg-gray-300';\n  };\n\n  // 模拟工作流执行\n  const executeWorkflow = async () => {\n    setIsRunning(true);\n    setWorkflowStatus('running');\n    setFailedStep(null);\n\n    // 从当前失败步骤或第一步开始\n    const startStep = failedStep !== null ? failedStep : 0;\n    setCurrentStep(startStep);\n\n    // 如果是重新开始，重置步骤状态\n    if (failedStep === null) {\n      setVisibleSteps(1);\n      setSteps(prev => prev.map(step => ({ ...step, status: 'pending' })));\n    }\n\n    for (let i = startStep; i < steps.length; i++) {\n      setCurrentStep(i);\n      \n      // 显示当前步骤（如果还未显示）\n      if (i + 1 > visibleSteps) {\n        setVisibleSteps(i + 1);\n        await new Promise(resolve => setTimeout(resolve, 300)); // 短暂延迟显示步骤\n      }\n      \n      // 设置当前步骤为运行中\n      setSteps(prev => prev.map((step, index) => \n        index === i ? { ...step, status: 'running' } : step\n      ));\n\n      // 等待步骤完成\n      await new Promise(resolve => setTimeout(resolve, steps[i].duration));\n\n      // 随机失败演示（15%概率，在第2或第3步）\n      const shouldFail = Math.random() < 0.15 && (i === 1 || i === 2);\n\n      if (shouldFail) {\n        setSteps(prev => prev.map((step, index) => \n          index === i ? { ...step, status: 'failed' } : step\n        ));\n        setWorkflowStatus('failed');\n        setFailedStep(i);\n        setIsRunning(false);\n        return;\n      }\n\n      // 设置步骤为完成\n      setSteps(prev => prev.map((step, index) => \n        index === i ? { ...step, status: 'completed' } : step\n      ));\n\n      // 完成一步后显示下一步（如果不是最后一步）\n      if (i < steps.length - 1) {\n        setVisibleSteps(i + 2);\n        await new Promise(resolve => setTimeout(resolve, 500)); // 延迟显示下一步\n      }\n    }\n\n    setWorkflowStatus('completed');\n    setFailedStep(null);\n    setIsRunning(false);\n  };\n\n  // 重试失败的步骤\n  const retryFromFailed = () => {\n    if (failedStep !== null) {\n      // 重置失败步骤的状态\n      setSteps(prev => prev.map((step, index) => \n        index === failedStep ? { ...step, status: 'pending' } : step\n      ));\n      executeWorkflow();\n    }\n  };\n\n  // 重置整个工作流\n  const resetWorkflow = () => {\n    setIsRunning(false);\n    setWorkflowStatus('idle');\n    setCurrentStep(0);\n    setVisibleSteps(1);\n    setFailedStep(null);\n    setSteps(workflowSteps.map(step => ({ ...step, status: 'pending' })));\n  };\n\n  return (\n    <div className=\"w-full\">\n      {/* AI对话框容器 */}\n      <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden border border-white/20\">\n        {/* 对话框头部 */}\n        <div className=\"bg-blue-500 text-white p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-sm font-bold\">AI</span>\n            </div>\n            <div>\n              <h3 className=\"font-semibold\">Temu自动化助手</h3>\n              <p className=\"text-blue-100 text-sm\">流量加速自动化执行中...</p>\n            </div>\n          </div>\n        </div>\n\n        {/* 对话内容 */}\n        <div className=\"p-6\">\n          {/* AI消息 */}\n          <div className=\"mb-6\">\n            <div className=\"bg-gray-100 rounded-lg p-4 mb-4\">\n              <p className=\"text-gray-800 mb-2\">好的，开始执行计划</p>\n              <div className=\"text-sm text-gray-600 bg-white rounded-lg p-3 border\">\n                <span className=\"font-medium\">执行配置：</span>高级流量加权档位，价格范围4-6美元，时效30天\n              </div>\n            </div>\n          </div>\n\n          {/* 优化后的时间线步骤列表 - 完美连接线 */}\n          <div className=\"relative\">\n            {steps.slice(0, visibleSteps).map((step, index) => (\n              <div key={step.id} className=\"relative\">\n                {/* 连接线 - 修复逻辑：只有当前步骤完成后才显示绿色 */}\n                {index < visibleSteps - 1 && (\n                  <div\n                    className={`absolute left-4 top-8 w-0.5 transition-all duration-500 z-0 ${getConnectionLineColor(index)}`}\n                    style={{\n                      height: 'calc(100% + 1.5rem)' // 精确延伸到下一个步骤的图标位置\n                    }}\n                  ></div>\n                )}\n\n                {/* 步骤内容容器 */}\n                <div className=\"flex items-start space-x-4 relative z-10 pb-6\">\n                  {/* 状态图标容器 - 优化层级确保完美覆盖 */}\n                  <div className=\"flex-shrink-0 relative\">\n                    {/* 图标背景圆圈 - 确保完全覆盖连接线 */}\n                    <div className=\"w-8 h-8 bg-white rounded-full absolute inset-0 z-10 border border-gray-100\"></div>\n\n                    {/* 状态图标 - 最高层级 */}\n                    <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 relative z-20 shadow-sm ${\n                      step.status === 'completed' ? 'bg-green-500 text-white' :\n                      step.status === 'failed' ? 'bg-red-500 text-white' :\n                      step.status === 'running' ? 'bg-blue-500 text-white' :\n                      'bg-gray-300 text-gray-600'\n                    }`}>\n                      {step.status === 'completed' && <CheckCircle className=\"w-5 h-5\" />}\n                      {step.status === 'failed' && <XCircle className=\"w-5 h-5\" />}\n                      {step.status === 'running' && <Loader2 className=\"w-5 h-5 animate-spin\" />}\n                      {step.status === 'pending' && <div className=\"w-3 h-3 rounded-full bg-gray-600\"></div>}\n                    </div>\n                  </div>\n\n                  {/* 步骤内容 */}\n                  <div className=\"flex-1 min-w-0 pt-1\">\n                    <div className=\"mb-2\">\n                      <h4 className=\"text-lg font-medium text-gray-900\">{step.name}</h4>\n                      {/* 失败状态的重试按钮 */}\n                      {step.status === 'failed' && (\n                        <button\n                          onClick={retryFromFailed}\n                          disabled={isRunning}\n                          className=\"mt-2 text-xs bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-3 py-1.5 rounded-full font-medium transition-all duration-200 hover:shadow-md hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-1\"\n                        >\n                          <RefreshCw className=\"w-3 h-3\" />\n                          <span>重试</span>\n                        </button>\n                      )}\n                    </div>\n\n                    <p className=\"text-sm text-gray-600 leading-relaxed\">\n                      {getStepDescription(step)}\n                    </p>\n\n                    {/* 失败时的错误信息 */}\n                    {step.status === 'failed' && (\n                      <div className=\"mt-2 bg-red-50 border border-red-200 rounded-lg p-3\">\n                        <p className=\"text-red-700 text-sm\">\n                          执行失败，可能是网络连接问题或系统繁忙，请点击重试继续执行。\n                        </p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* 操作按钮区域 */}\n          <div className=\"mt-8 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                {workflowStatus === 'idle' && (\n                  <button\n                    onClick={executeWorkflow}\n                    disabled={isRunning}\n                    className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                  >\n                    <Play className=\"w-4 h-4\" />\n                    <span>开始执行</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'running' && (\n                  <button\n                    disabled\n                    className=\"bg-gradient-to-r from-gray-400 to-gray-500 text-white px-6 py-3 rounded-xl text-sm font-medium cursor-not-allowed flex items-center space-x-2\"\n                  >\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span>执行中...</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'completed' && (\n                  <button\n                    onClick={resetWorkflow}\n                    className=\"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                  >\n                    <CheckCircle className=\"w-4 h-4\" />\n                    <span>执行完成</span>\n                  </button>\n                )}\n\n                {workflowStatus === 'failed' && (\n                  <div className=\"flex items-center space-x-3\">\n                    <button\n                      onClick={retryFromFailed}\n                      disabled={isRunning}\n                      className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2\"\n                    >\n                      <RefreshCw className=\"w-4 h-4\" />\n                      <span>重试</span>\n                    </button>\n                    <button\n                      onClick={resetWorkflow}\n                      className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0\"\n                    >\n                      重新开始\n                    </button>\n                  </div>\n                )}\n\n                {/* 整体进度指示 */}\n                {workflowStatus === 'running' && (\n                  <div className=\"flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-4 py-3 border border-blue-200/50 shadow-sm\">\n                    <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center\">\n                      <Loader2 className=\"w-4 h-4 text-white animate-spin\" />\n                    </div>\n                    <span className=\"text-sm text-blue-700 font-medium\">\n                      正在执行第 {currentStep + 1} 步，共 {steps.length} 步\n                    </span>\n                  </div>\n                )}\n              </div>\n\n              {/* 刷新按钮 */}\n              <button\n                onClick={resetWorkflow}\n                className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:shadow-sm\"\n                title=\"重新开始\"\n              >\n                <RefreshCw className=\"w-4 h-4\" />\n              </button>\n            </div>\n\n            {/* 状态指示器 */}\n            {workflowStatus === 'completed' && (\n              <div className=\"mt-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <CheckCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-green-700 font-semibold text-base mb-1\">\n                      🎉 自动化执行完成！\n                    </div>\n                    <div className=\"text-green-600 text-sm\">\n                      所有商品已成功配置流量加速，系统运行正常\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {workflowStatus === 'failed' && failedStep !== null && (\n              <div className=\"mt-6 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg\">\n                    <AlertCircle className=\"w-5 h-5 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-red-700 font-semibold text-base mb-1\">\n                      执行中断，需要处理\n                    </div>\n                    <div className=\"text-red-600 text-sm\">\n                      第 {failedStep + 1} 步出现问题，可点击重试继续执行\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Demo控制面板 */}\n      <div className=\"mt-6 bg-gradient-to-r from-slate-50 to-blue-50 rounded-2xl shadow-lg p-6 border border-white/20\">\n        <h4 className=\"font-semibold text-gray-900 mb-3 flex items-center\">\n          <div className=\"w-2 h-2 bg-blue-500 rounded-full mr-2\"></div>\n          Demo控制面板\n        </h4>\n        <p className=\"text-sm text-gray-600 mb-4 leading-relaxed\">\n          <span className=\"font-medium\">演示特性：</span>\n          <span className=\"inline-flex items-center ml-2 space-x-3\">\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />动态描述</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />时间线</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />失败重试</span>\n            <span className=\"flex items-center\"><CheckCircle className=\"w-3 h-3 text-green-500 mr-1\" />完美连接</span>\n          </span>\n        </p>\n        <div className=\"flex flex-wrap gap-3\">\n          <button\n            onClick={executeWorkflow}\n            disabled={isRunning}\n            className=\"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>🚀</span>\n            <span>模拟执行</span>\n          </button>\n          <button\n            onClick={resetWorkflow}\n            className=\"bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>🔄</span>\n            <span>重置状态</span>\n          </button>\n          <button\n            onClick={() => {\n              // 快速演示失败场景\n              setVisibleSteps(3);\n              setSteps(prev => prev.map((step, index) => {\n                if (index === 0) return { ...step, status: 'completed' };\n                if (index === 1) return { ...step, status: 'completed' };\n                if (index === 2) return { ...step, status: 'failed' };\n                return step;\n              }));\n              setWorkflowStatus('failed');\n              setFailedStep(2);\n            }}\n            className=\"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 flex items-center space-x-2\"\n          >\n            <span>⚠️</span>\n            <span>演示失败</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TemuWorkflowDemo;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,SAAS,CAAEC,WAAW,CAAEC,OAAO,CAAEC,WAAW,CAAEC,OAAO,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3F,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGb,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAACc,SAAS,CAAEC,YAAY,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgB,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAC,MAAM,CAAC,CAAE;AAC9D,KAAM,CAACkB,YAAY,CAAEC,eAAe,CAAC,CAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAE;AACrD,KAAM,CAACoB,UAAU,CAAEC,aAAa,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAAE;AAEpD;AACA,KAAM,CAAAsB,aAAa,CAAG,CACpB,CACEC,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,IAAI,CACVC,kBAAkB,CAAE,aAAa,CACjCC,oBAAoB,CAAE,qBAAqB,CAC3CC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,SACV,CAAC,CACD,CACEL,EAAE,CAAE,gBAAgB,CACpBC,IAAI,CAAE,MAAM,CACZC,kBAAkB,CAAE,gBAAgB,CACpCC,oBAAoB,CAAE,2CAA2C,CACjEC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,SACV,CAAC,CACD,CACEL,EAAE,CAAE,oBAAoB,CACxBC,IAAI,CAAE,MAAM,CACZC,kBAAkB,CAAE,gBAAgB,CACpCC,oBAAoB,CAAE,2BAA2B,CACjDC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,SACV,CAAC,CACD,CACEL,EAAE,CAAE,QAAQ,CACZC,IAAI,CAAE,IAAI,CACVC,kBAAkB,CAAE,aAAa,CACjCC,oBAAoB,CAAE,0CAA0C,CAChEC,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,SACV,CAAC,CACF,CAED,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAG9B,QAAQ,CAACsB,aAAa,CAAC,CAEjD;AACA,KAAM,CAAAS,kBAAkB,CAAIC,IAAI,EAAK,CACnC,GAAIA,IAAI,CAACJ,MAAM,GAAK,SAAS,CAAE,CAC7B,MAAO,CAAAI,IAAI,CAACP,kBAAkB,CAChC,CAAC,IAAM,IAAIO,IAAI,CAACJ,MAAM,GAAK,WAAW,CAAE,CACtC,MAAO,CAAAI,IAAI,CAACN,oBAAoB,CAClC,CAAC,IAAM,IAAIM,IAAI,CAACJ,MAAM,GAAK,QAAQ,CAAE,CACnC,MAAO,QAAQI,IAAI,CAACP,kBAAkB,EAAE,CAC1C,CACA,MAAO,SAAS,CAClB,CAAC,CAED;AACA,KAAM,CAAAQ,sBAAsB,CAAIC,SAAS,EAAK,CAC5C,KAAM,CAAAF,IAAI,CAAGH,KAAK,CAACK,SAAS,CAAC,CAC7B,GAAIF,IAAI,CAACJ,MAAM,GAAK,WAAW,CAAE,CAC/B,MAAO,cAAc,CACvB,CAAC,IAAM,IAAII,IAAI,CAACJ,MAAM,GAAK,QAAQ,CAAE,CACnC,MAAO,YAAY,CACrB,CAAC,IAAM,IAAII,IAAI,CAACJ,MAAM,GAAK,SAAS,CAAE,CACpC,MAAO,aAAa,CACtB,CACA,MAAO,aAAa,CACtB,CAAC,CAED;AACA,KAAM,CAAAO,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClCpB,YAAY,CAAC,IAAI,CAAC,CAClBE,iBAAiB,CAAC,SAAS,CAAC,CAC5BI,aAAa,CAAC,IAAI,CAAC,CAEnB;AACA,KAAM,CAAAe,SAAS,CAAGhB,UAAU,GAAK,IAAI,CAAGA,UAAU,CAAG,CAAC,CACtDP,cAAc,CAACuB,SAAS,CAAC,CAEzB;AACA,GAAIhB,UAAU,GAAK,IAAI,CAAE,CACvBD,eAAe,CAAC,CAAC,CAAC,CAClBW,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAACC,GAAG,CAACN,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEJ,MAAM,CAAE,SAAU,CAAC,CAAC,CAAC,CAAC,CACtE,CAEA,IAAK,GAAI,CAAAW,CAAC,CAAGH,SAAS,CAAEG,CAAC,CAAGV,KAAK,CAACW,MAAM,CAAED,CAAC,EAAE,CAAE,CAC7C1B,cAAc,CAAC0B,CAAC,CAAC,CAEjB;AACA,GAAIA,CAAC,CAAG,CAAC,CAAGrB,YAAY,CAAE,CACxBC,eAAe,CAACoB,CAAC,CAAG,CAAC,CAAC,CACtB,KAAM,IAAI,CAAAE,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAAE;AAC1D,CAEA;AACAZ,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAACC,GAAG,CAAC,CAACN,IAAI,CAAEY,KAAK,GACpCA,KAAK,GAAKL,CAAC,CAAG,CAAE,GAAGP,IAAI,CAAEJ,MAAM,CAAE,SAAU,CAAC,CAAGI,IACjD,CAAC,CAAC,CAEF;AACA,KAAM,IAAI,CAAAS,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAEb,KAAK,CAACU,CAAC,CAAC,CAACZ,QAAQ,CAAC,CAAC,CAEpE;AACA,KAAM,CAAAkB,UAAU,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,IAAI,GAAKR,CAAC,GAAK,CAAC,EAAIA,CAAC,GAAK,CAAC,CAAC,CAE/D,GAAIM,UAAU,CAAE,CACdf,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAACC,GAAG,CAAC,CAACN,IAAI,CAAEY,KAAK,GACpCA,KAAK,GAAKL,CAAC,CAAG,CAAE,GAAGP,IAAI,CAAEJ,MAAM,CAAE,QAAS,CAAC,CAAGI,IAChD,CAAC,CAAC,CACFf,iBAAiB,CAAC,QAAQ,CAAC,CAC3BI,aAAa,CAACkB,CAAC,CAAC,CAChBxB,YAAY,CAAC,KAAK,CAAC,CACnB,OACF,CAEA;AACAe,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAACC,GAAG,CAAC,CAACN,IAAI,CAAEY,KAAK,GACpCA,KAAK,GAAKL,CAAC,CAAG,CAAE,GAAGP,IAAI,CAAEJ,MAAM,CAAE,WAAY,CAAC,CAAGI,IACnD,CAAC,CAAC,CAEF;AACA,GAAIO,CAAC,CAAGV,KAAK,CAACW,MAAM,CAAG,CAAC,CAAE,CACxBrB,eAAe,CAACoB,CAAC,CAAG,CAAC,CAAC,CACtB,KAAM,IAAI,CAAAE,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAAE;AAC1D,CACF,CAEAzB,iBAAiB,CAAC,WAAW,CAAC,CAC9BI,aAAa,CAAC,IAAI,CAAC,CACnBN,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAiC,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI5B,UAAU,GAAK,IAAI,CAAE,CACvB;AACAU,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAACC,GAAG,CAAC,CAACN,IAAI,CAAEY,KAAK,GACpCA,KAAK,GAAKxB,UAAU,CAAG,CAAE,GAAGY,IAAI,CAAEJ,MAAM,CAAE,SAAU,CAAC,CAAGI,IAC1D,CAAC,CAAC,CACFG,eAAe,CAAC,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAc,aAAa,CAAGA,CAAA,GAAM,CAC1BlC,YAAY,CAAC,KAAK,CAAC,CACnBE,iBAAiB,CAAC,MAAM,CAAC,CACzBJ,cAAc,CAAC,CAAC,CAAC,CACjBM,eAAe,CAAC,CAAC,CAAC,CAClBE,aAAa,CAAC,IAAI,CAAC,CACnBS,QAAQ,CAACR,aAAa,CAACgB,GAAG,CAACN,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEJ,MAAM,CAAE,SAAU,CAAC,CAAC,CAAC,CAAC,CACvE,CAAC,CAED,mBACElB,KAAA,QAAKwC,SAAS,CAAC,QAAQ,CAAAC,QAAA,eAErBzC,KAAA,QAAKwC,SAAS,CAAC,2FAA2F,CAAAC,QAAA,eAExG3C,IAAA,QAAK0C,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCzC,KAAA,QAAKwC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3C,IAAA,QAAK0C,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChF3C,IAAA,SAAM0C,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,IAAE,CAAM,CAAC,CAC1C,CAAC,cACNzC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,OAAI0C,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oCAAS,CAAI,CAAC,cAC5C3C,IAAA,MAAG0C,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iEAAa,CAAG,CAAC,EACnD,CAAC,EACH,CAAC,CACH,CAAC,cAGNzC,KAAA,QAAKwC,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElB3C,IAAA,QAAK0C,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBzC,KAAA,QAAKwC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C3C,IAAA,MAAG0C,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,wDAAS,CAAG,CAAC,cAC/CzC,KAAA,QAAKwC,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnE3C,IAAA,SAAM0C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,gCAAK,CAAM,CAAC,0HAC5C,EAAK,CAAC,EACH,CAAC,CACH,CAAC,cAGN3C,IAAA,QAAK0C,SAAS,CAAC,UAAU,CAAAC,QAAA,CACtBtB,KAAK,CAACuB,KAAK,CAAC,CAAC,CAAElC,YAAY,CAAC,CAACoB,GAAG,CAAC,CAACN,IAAI,CAAEY,KAAK,gBAC5ClC,KAAA,QAAmBwC,SAAS,CAAC,UAAU,CAAAC,QAAA,EAEpCP,KAAK,CAAG1B,YAAY,CAAG,CAAC,eACvBV,IAAA,QACE0C,SAAS,CAAE,+DAA+DjB,sBAAsB,CAACW,KAAK,CAAC,EAAG,CAC1GS,KAAK,CAAE,CACLC,MAAM,CAAE,qBAAsB;AAChC,CAAE,CACE,CACP,cAGD5C,KAAA,QAAKwC,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAE5DzC,KAAA,QAAKwC,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAErC3C,IAAA,QAAK0C,SAAS,CAAC,4EAA4E,CAAM,CAAC,cAGlGxC,KAAA,QAAKwC,SAAS,CAAE,6GACdlB,IAAI,CAACJ,MAAM,GAAK,WAAW,CAAG,yBAAyB,CACvDI,IAAI,CAACJ,MAAM,GAAK,QAAQ,CAAG,uBAAuB,CAClDI,IAAI,CAACJ,MAAM,GAAK,SAAS,CAAG,wBAAwB,CACpD,2BAA2B,EAC1B,CAAAuB,QAAA,EACAnB,IAAI,CAACJ,MAAM,GAAK,WAAW,eAAIpB,IAAA,CAACL,WAAW,EAAC+C,SAAS,CAAC,SAAS,CAAE,CAAC,CAClElB,IAAI,CAACJ,MAAM,GAAK,QAAQ,eAAIpB,IAAA,CAACJ,OAAO,EAAC8C,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3DlB,IAAI,CAACJ,MAAM,GAAK,SAAS,eAAIpB,IAAA,CAACF,OAAO,EAAC4C,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACzElB,IAAI,CAACJ,MAAM,GAAK,SAAS,eAAIpB,IAAA,QAAK0C,SAAS,CAAC,kCAAkC,CAAM,CAAC,EACnF,CAAC,EACH,CAAC,cAGNxC,KAAA,QAAKwC,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCzC,KAAA,QAAKwC,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB3C,IAAA,OAAI0C,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEnB,IAAI,CAACR,IAAI,CAAK,CAAC,CAEjEQ,IAAI,CAACJ,MAAM,GAAK,QAAQ,eACvBlB,KAAA,WACE6C,OAAO,CAAEP,eAAgB,CACzBQ,QAAQ,CAAE1C,SAAU,CACpBoC,SAAS,CAAC,2TAA2T,CAAAC,QAAA,eAErU3C,IAAA,CAACN,SAAS,EAACgD,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC1C,IAAA,SAAA2C,QAAA,CAAM,cAAE,CAAM,CAAC,EACT,CACT,EACE,CAAC,cAEN3C,IAAA,MAAG0C,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACjDpB,kBAAkB,CAACC,IAAI,CAAC,CACxB,CAAC,CAGHA,IAAI,CAACJ,MAAM,GAAK,QAAQ,eACvBpB,IAAA,QAAK0C,SAAS,CAAC,qDAAqD,CAAAC,QAAA,cAClE3C,IAAA,MAAG0C,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,sLAEpC,CAAG,CAAC,CACD,CACN,EACE,CAAC,EACH,CAAC,GA9DEnB,IAAI,CAACT,EA+DV,CACN,CAAC,CACC,CAAC,cAGNb,KAAA,QAAKwC,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDzC,KAAA,QAAKwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzC,KAAA,QAAKwC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EACzCnC,cAAc,GAAK,MAAM,eACxBN,KAAA,WACE6C,OAAO,CAAEpB,eAAgB,CACzBqB,QAAQ,CAAE1C,SAAU,CACpBoC,SAAS,CAAC,0SAA0S,CAAAC,QAAA,eAEpT3C,IAAA,CAACP,IAAI,EAACiD,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5B1C,IAAA,SAAA2C,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CACT,CAEAnC,cAAc,GAAK,SAAS,eAC3BN,KAAA,WACE8C,QAAQ,MACRN,SAAS,CAAC,+IAA+I,CAAAC,QAAA,eAEzJ3C,IAAA,CAACF,OAAO,EAAC4C,SAAS,CAAC,sBAAsB,CAAE,CAAC,cAC5C1C,IAAA,SAAA2C,QAAA,CAAM,uBAAM,CAAM,CAAC,EACb,CACT,CAEAnC,cAAc,GAAK,WAAW,eAC7BN,KAAA,WACE6C,OAAO,CAAEN,aAAc,CACvBC,SAAS,CAAC,8PAA8P,CAAAC,QAAA,eAExQ3C,IAAA,CAACL,WAAW,EAAC+C,SAAS,CAAC,SAAS,CAAE,CAAC,cACnC1C,IAAA,SAAA2C,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CACT,CAEAnC,cAAc,GAAK,QAAQ,eAC1BN,KAAA,QAAKwC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CzC,KAAA,WACE6C,OAAO,CAAEP,eAAgB,CACzBQ,QAAQ,CAAE1C,SAAU,CACpBoC,SAAS,CAAC,kTAAkT,CAAAC,QAAA,eAE5T3C,IAAA,CAACN,SAAS,EAACgD,SAAS,CAAC,SAAS,CAAE,CAAC,cACjC1C,IAAA,SAAA2C,QAAA,CAAM,cAAE,CAAM,CAAC,EACT,CAAC,cACT3C,IAAA,WACE+C,OAAO,CAAEN,aAAc,CACvBC,SAAS,CAAC,8NAA8N,CAAAC,QAAA,CACzO,0BAED,CAAQ,CAAC,EACN,CACN,CAGAnC,cAAc,GAAK,SAAS,eAC3BN,KAAA,QAAKwC,SAAS,CAAC,+HAA+H,CAAAC,QAAA,eAC5I3C,IAAA,QAAK0C,SAAS,CAAC,kGAAkG,CAAAC,QAAA,cAC/G3C,IAAA,CAACF,OAAO,EAAC4C,SAAS,CAAC,iCAAiC,CAAE,CAAC,CACpD,CAAC,cACNxC,KAAA,SAAMwC,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,iCAC5C,CAACvC,WAAW,CAAG,CAAC,CAAC,sBAAK,CAACiB,KAAK,CAACW,MAAM,CAAC,SAC5C,EAAM,CAAC,EACJ,CACN,EACE,CAAC,cAGNhC,IAAA,WACE+C,OAAO,CAAEN,aAAc,CACvBC,SAAS,CAAC,gHAAgH,CAC1HO,KAAK,CAAC,0BAAM,CAAAN,QAAA,cAEZ3C,IAAA,CAACN,SAAS,EAACgD,SAAS,CAAC,SAAS,CAAE,CAAC,CAC3B,CAAC,EACN,CAAC,CAGLlC,cAAc,GAAK,WAAW,eAC7BR,IAAA,QAAK0C,SAAS,CAAC,uGAAuG,CAAAC,QAAA,cACpHzC,KAAA,QAAKwC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3C,IAAA,QAAK0C,SAAS,CAAC,kHAAkH,CAAAC,QAAA,cAC/H3C,IAAA,CAACL,WAAW,EAAC+C,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC3C,CAAC,cACNxC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,QAAK0C,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CAAC,+DAE7D,CAAK,CAAC,cACN3C,IAAA,QAAK0C,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,0HAExC,CAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,CAEAnC,cAAc,GAAK,QAAQ,EAAII,UAAU,GAAK,IAAI,eACjDZ,IAAA,QAAK0C,SAAS,CAAC,kGAAkG,CAAAC,QAAA,cAC/GzC,KAAA,QAAKwC,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C3C,IAAA,QAAK0C,SAAS,CAAC,+GAA+G,CAAAC,QAAA,cAC5H3C,IAAA,CAACH,WAAW,EAAC6C,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC3C,CAAC,cACNxC,KAAA,QAAAyC,QAAA,eACE3C,IAAA,QAAK0C,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,wDAE3D,CAAK,CAAC,cACNzC,KAAA,QAAKwC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,EAAC,SAClC,CAAC/B,UAAU,CAAG,CAAC,CAAC,6FACpB,EAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,cAGNV,KAAA,QAAKwC,SAAS,CAAC,iGAAiG,CAAAC,QAAA,eAC9GzC,KAAA,OAAIwC,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eAChE3C,IAAA,QAAK0C,SAAS,CAAC,uCAAuC,CAAM,CAAC,+BAE/D,EAAI,CAAC,cACLxC,KAAA,MAAGwC,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACvD3C,IAAA,SAAM0C,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,gCAAK,CAAM,CAAC,cAC1CzC,KAAA,SAAMwC,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACvDzC,KAAA,SAAMwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAAC3C,IAAA,CAACL,WAAW,EAAC+C,SAAS,CAAC,6BAA6B,CAAE,CAAC,2BAAI,EAAM,CAAC,cACtGxC,KAAA,SAAMwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAAC3C,IAAA,CAACL,WAAW,EAAC+C,SAAS,CAAC,6BAA6B,CAAE,CAAC,qBAAG,EAAM,CAAC,cACrGxC,KAAA,SAAMwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAAC3C,IAAA,CAACL,WAAW,EAAC+C,SAAS,CAAC,6BAA6B,CAAE,CAAC,2BAAI,EAAM,CAAC,cACtGxC,KAAA,SAAMwC,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAAC3C,IAAA,CAACL,WAAW,EAAC+C,SAAS,CAAC,6BAA6B,CAAE,CAAC,2BAAI,EAAM,CAAC,EAClG,CAAC,EACN,CAAC,cACJxC,KAAA,QAAKwC,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnCzC,KAAA,WACE6C,OAAO,CAAEpB,eAAgB,CACzBqB,QAAQ,CAAE1C,SAAU,CACpBoC,SAAS,CAAC,qRAAqR,CAAAC,QAAA,eAE/R3C,IAAA,SAAA2C,QAAA,CAAM,cAAE,CAAM,CAAC,cACf3C,IAAA,SAAA2C,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CAAC,cACTzC,KAAA,WACE6C,OAAO,CAAEN,aAAc,CACvBC,SAAS,CAAC,qOAAqO,CAAAC,QAAA,eAE/O3C,IAAA,SAAA2C,QAAA,CAAM,cAAE,CAAM,CAAC,cACf3C,IAAA,SAAA2C,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CAAC,cACTzC,KAAA,WACE6C,OAAO,CAAEA,CAAA,GAAM,CACb;AACApC,eAAe,CAAC,CAAC,CAAC,CAClBW,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAACC,GAAG,CAAC,CAACN,IAAI,CAAEY,KAAK,GAAK,CACzC,GAAIA,KAAK,GAAK,CAAC,CAAE,MAAO,CAAE,GAAGZ,IAAI,CAAEJ,MAAM,CAAE,WAAY,CAAC,CACxD,GAAIgB,KAAK,GAAK,CAAC,CAAE,MAAO,CAAE,GAAGZ,IAAI,CAAEJ,MAAM,CAAE,WAAY,CAAC,CACxD,GAAIgB,KAAK,GAAK,CAAC,CAAE,MAAO,CAAE,GAAGZ,IAAI,CAAEJ,MAAM,CAAE,QAAS,CAAC,CACrD,MAAO,CAAAI,IAAI,CACb,CAAC,CAAC,CAAC,CACHf,iBAAiB,CAAC,QAAQ,CAAC,CAC3BI,aAAa,CAAC,CAAC,CAAC,CAClB,CAAE,CACF6B,SAAS,CAAC,6OAA6O,CAAAC,QAAA,eAEvP3C,IAAA,SAAA2C,QAAA,CAAM,cAAE,CAAM,CAAC,cACf3C,IAAA,SAAA2C,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}