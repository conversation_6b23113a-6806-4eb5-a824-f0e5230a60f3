[{"/Users/<USER>/Documents/augment-projects/ts/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/ts/src/App.jsx": "2", "/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx": "3", "/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx": "4", "/Users/<USER>/Documents/augment-projects/ts/src/TestPage.jsx": "5"}, {"size": 254, "mtime": 1751939357501, "results": "6", "hashOfConfig": "7"}, {"size": 195, "mtime": 1751944121122, "results": "8", "hashOfConfig": "7"}, {"size": 16977, "mtime": 1751941604103, "results": "9", "hashOfConfig": "7"}, {"size": 22412, "mtime": 1751945003630, "results": "10", "hashOfConfig": "7"}, {"size": 2875, "mtime": 1751945058891, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1200vd8", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/ts/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/ts/src/App.jsx", [], [], "/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx", [], [], "/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx", [], [], "/Users/<USER>/Documents/augment-projects/ts/src/TestPage.jsx", [], []]