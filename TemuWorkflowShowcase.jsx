import React, { useState } from 'react';
import { 
  Play, RefreshCw, CheckCircle, XCircle, Loader2, 
  Zap, Target, Shield, Clock, ArrowRight, Star,
  Sparkles, TrendingUp, Users, Award
} from 'lucide-react';
import TemuWorkflowDemo from './TemuWorkflowDemo';

const TemuWorkflowShowcase = () => {
  const [activeTab, setActiveTab] = useState('demo');

  const features = [
    {
      icon: <Zap className="w-6 h-6" />,
      title: '智能自动化',
      description: '全自动执行流量加速配置，无需人工干预',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: '精准筛选',
      description: '智能筛选符合条件的商品，提高投放效率',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: '安全可靠',
      description: '多重验证机制，确保操作安全性',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: '实时监控',
      description: '实时显示执行进度，支持失败重试',
      color: 'from-orange-500 to-red-500'
    }
  ];

  const highlights = [
    {
      icon: <Sparkles className="w-5 h-5" />,
      text: '动态描述变化',
      detail: '每个步骤都有详细的执行状态描述'
    },
    {
      icon: <TrendingUp className="w-5 h-5" />,
      text: '逐步显示时间线',
      detail: '优雅的步骤展示动画效果'
    },
    {
      icon: <RefreshCw className="w-5 h-5" />,
      text: '智能失败重试',
      detail: '自动检测失败并支持一键重试'
    },
    {
      icon: <Award className="w-5 h-5" />,
      text: '完美连接线',
      detail: '精美的时间线视觉设计'
    }
  ];

  const stats = [
    { label: '处理商品', value: '200+', icon: <Users className="w-5 h-5" /> },
    { label: '成功率', value: '93%', icon: <TrendingUp className="w-5 h-5" /> },
    { label: '平均耗时', value: '10s', icon: <Clock className="w-5 h-5" /> },
    { label: '节省时间', value: '95%', icon: <Star className="w-5 h-5" /> }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* 头部区域 */}
      <div className="relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl"></div>
        <div className="absolute top-0 right-1/4 w-72 h-72 bg-purple-400/20 rounded-full blur-3xl"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-24">
          {/* 主标题区域 */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
              <Sparkles className="w-4 h-4 mr-2" />
              AI驱动的电商自动化解决方案
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Temu流量加速
              </span>
              <br />
              <span className="text-gray-800">自动化工作流</span>
            </h1>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
              体验智能化的Temu商品流量加速配置流程，从登录验证到商品筛选，再到流量加权设置，
              全程自动化执行，让您的电商运营更加高效便捷。
            </p>

            {/* 统计数据 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-2xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20">
                  <div className="flex items-center justify-center mb-2 text-blue-600">
                    {stat.icon}
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>

          {/* 功能特性卡片 */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {features.map((feature, index) => (
              <div key={index} className="group">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    {feature.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {/* 标签页导航 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-1 shadow-lg border border-white/20">
            <div className="flex space-x-1">
              <button
                onClick={() => setActiveTab('demo')}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === 'demo'
                    ? 'bg-blue-500 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Play className="w-4 h-4 inline mr-2" />
                实时演示
              </button>
              <button
                onClick={() => setActiveTab('features')}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === 'features'
                    ? 'bg-blue-500 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Star className="w-4 h-4 inline mr-2" />
                功能特性
              </button>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        {activeTab === 'demo' && (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* 演示区域 */}
            <div className="lg:col-span-2">
              <TemuWorkflowDemo />
            </div>

            {/* 侧边栏信息 */}
            <div className="space-y-6">
              {/* 操作指南 */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Target className="w-5 h-5 mr-2 text-blue-500" />
                  操作指南
                </h3>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">1</div>
                    <p className="text-sm text-gray-600">点击"开始执行"按钮启动自动化流程</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">2</div>
                    <p className="text-sm text-gray-600">观察每个步骤的实时执行状态和描述</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">3</div>
                    <p className="text-sm text-gray-600">如遇失败可点击重试继续执行</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">4</div>
                    <p className="text-sm text-gray-600">使用Demo控制面板体验不同场景</p>
                  </div>
                </div>
              </div>

              {/* 功能亮点 */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-purple-500" />
                  功能亮点
                </h3>
                <div className="space-y-4">
                  {highlights.map((highlight, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="text-purple-500 mt-0.5">
                        {highlight.icon}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 text-sm">{highlight.text}</div>
                        <div className="text-xs text-gray-600 mt-1">{highlight.detail}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 使用场景 */}
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white shadow-lg">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  适用场景
                </h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center">
                    <ArrowRight className="w-4 h-4 mr-2 opacity-70" />
                    大批量商品流量加速配置
                  </li>
                  <li className="flex items-center">
                    <ArrowRight className="w-4 h-4 mr-2 opacity-70" />
                    定期营销活动自动化执行
                  </li>
                  <li className="flex items-center">
                    <ArrowRight className="w-4 h-4 mr-2 opacity-70" />
                    多店铺统一运营管理
                  </li>
                  <li className="flex items-center">
                    <ArrowRight className="w-4 h-4 mr-2 opacity-70" />
                    电商运营效率提升
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'features' && (
          <div className="max-w-4xl mx-auto">
            {/* 详细功能介绍 */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20">
              <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">核心功能详解</h2>
              
              <div className="space-y-8">
                {/* 工作流步骤详解 */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Zap className="w-6 h-6 mr-3 text-blue-500" />
                    自动化工作流程
                  </h3>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
                        <div className="flex items-center mb-2">
                          <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">1</div>
                          <h4 className="font-semibold text-gray-900">智能登录验证</h4>
                        </div>
                        <p className="text-sm text-gray-600 ml-11">自动验证Temu账号信息，确保安全登录并获取店铺权限</p>
                      </div>
                      
                      <div className="bg-purple-50 rounded-xl p-4 border border-purple-100">
                        <div className="flex items-center mb-2">
                          <div className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">2</div>
                          <h4 className="font-semibold text-gray-900">精准商品筛选</h4>
                        </div>
                        <p className="text-sm text-gray-600 ml-11">根据设定条件智能筛选符合要求的商品，提高投放精准度</p>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="bg-green-50 rounded-xl p-4 border border-green-100">
                        <div className="flex items-center mb-2">
                          <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">3</div>
                          <h4 className="font-semibold text-gray-900">流量加权配置</h4>
                        </div>
                        <p className="text-sm text-gray-600 ml-11">为筛选出的商品自动设置流量加速权重，优化曝光效果</p>
                      </div>
                      
                      <div className="bg-orange-50 rounded-xl p-4 border border-orange-100">
                        <div className="flex items-center mb-2">
                          <div className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">4</div>
                          <h4 className="font-semibold text-gray-900">执行结果统计</h4>
                        </div>
                        <p className="text-sm text-gray-600 ml-11">生成详细的执行报告，包含成功数量、费用统计等信息</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 技术特性 */}
                <div className="border-t border-gray-200 pt-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                    <Shield className="w-6 h-6 mr-3 text-green-500" />
                    技术特性
                  </h3>
                  
                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <Loader2 className="w-8 h-8 text-white" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">实时状态监控</h4>
                      <p className="text-sm text-gray-600">每个步骤都有详细的状态反馈和进度显示</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <RefreshCw className="w-8 h-8 text-white" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">智能错误恢复</h4>
                      <p className="text-sm text-gray-600">自动检测执行失败并支持一键重试功能</p>
                    </div>
                    
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <CheckCircle className="w-8 h-8 text-white" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">优雅用户体验</h4>
                      <p className="text-sm text-gray-600">流畅的动画效果和直观的界面设计</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TemuWorkflowShowcase;
